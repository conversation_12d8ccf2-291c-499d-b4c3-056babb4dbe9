import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Alignment, Border, Side
import os

def process_csv_to_excel(csv_file:str='output.csv'):
    """
    处理CSV文件转换为Excel并进行格式化
    """
    # 1. 读取CSV文件
    df = pd.read_csv(csv_file)
    
        
    output_file = f'{csv_file}.xlsx'
    writie_to_excel(df, output_file)
def writie_to_excel(df:pd.DataFrame, output_file:str):
    # 3. 创建Excel文件
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    # 4. 加载工作簿进行格式化
    wb = load_workbook(output_file)
    ws = wb.active
    
    # 5. 合并单元格处理
    merged_columns = []  # 新增：记录被合并的列索引
    merge_cells_by_rules(ws, df, merged_columns)  # 修改：传递merged_columns参数
    
    # 6. 调整列宽
    adjust_column_widths(ws, df, merged_columns)  # 修改：传递merged_columns参数
    
    # 7. 单元格加外边框
    set_border_for_all_cells(ws, len(df) + 1, len(df.columns))  # 新增: 调用设置边框函数
    
    # 8. 保存文件
    wb.save(output_file)
    print(f"已生成格式化的Excel文件: {output_file}")

def merge_cells_by_rules(ws, df, merged_columns):
    """
    根据规则合并单元格
    """
    # 找到"子过程描述"列的位置
    subprocess_desc_col = None
    for idx, col in enumerate(df.columns):
        if col == '子过程描述':
            subprocess_desc_col = idx + 1  # openpyxl使用1基索引
            break
    
    # 新增：找到"预估工作量"列的位置
    estimated_workload_col = None
    for idx, col in enumerate(df.columns):
        if '预估工作量' in col :
            estimated_workload_col = idx + 1
            break
    
    if subprocess_desc_col is None:
        print("未找到'子过程描述'列")
        return
    
    # 需要合并的列（从"功能过程"列往前的所有列）
    merge_col_name = "功能过程"
    merge_columns = []
    for idx, col in enumerate(df.columns):
        if col == merge_col_name:
            merge_columns = list(range(1, idx + 2))
            break
    
    # 对每个需要合并的列进行处理
    for col_idx in merge_columns:
        # 如果是预估工作量列，则启用仅合并空行模式
        if estimated_workload_col and col_idx == estimated_workload_col:
            merge_cols_with_previous(ws, col_idx, len(df) + 1, only_merge_empty=True)
        else:
            merge_cols_with_previous(ws, col_idx, len(df) + 1)
        merged_columns.append(col_idx)  # 新增：记录被合并的列
    
    # 为所有单元格设置自动换行
    set_wrap_text_for_all_cells(ws, len(df) + 1, len(df.columns))

def merge_cols_with_previous(ws, col_idx, total_rows, only_merge_empty=False):
    """
    将连续的空单元格与前一个非空单元格合并，同时合并连续相同值的单元格
    """
    current_row = 2  # 从第2行开始（跳过标题）
    
    while current_row <= total_rows:
        cell_value = ws.cell(row=current_row, column=col_idx).value
        
        # 如果当前单元格有值，查找后续连续的空单元格或相同值单元格
        if cell_value is not None and str(cell_value).strip() != '':
            start_row = current_row
            end_row = current_row
            
            # 查找连续的空单元格或相同值单元格
            while end_row + 1 <= total_rows:
                next_cell_value = ws.cell(row=end_row + 1, column=col_idx).value
                # 如果是仅合并空行模式
                if only_merge_empty:
                    # 仅当后续单元格为空时合并
                    if (next_cell_value is None or str(next_cell_value).strip() == ''):
                        end_row += 1
                    else:
                        break
                # 原有合并逻辑（空行+相同值）
                else:
                    if (next_cell_value is None or str(next_cell_value).strip() == '') \
                            or (str(next_cell_value).strip() == str(cell_value).strip()):
                        end_row += 1
                    else:
                        break
            
            # 如果有连续的空单元格或相同值单元格，则合并
            if end_row > start_row:
                ws.merge_cells(start_row=start_row, start_column=col_idx,
                             end_row=end_row, end_column=col_idx)
                # 设置居中对齐和自动换行
                merged_cell = ws.cell(row=start_row, column=col_idx)
                merged_cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
            
            current_row = end_row + 1
        else:
            current_row += 1

def set_wrap_text_for_all_cells(ws, total_rows, total_cols):
    """
    为所有单元格设置自动换行
    """
    for row in range(1, total_rows + 1):
        for col in range(1, total_cols + 1):
            cell = ws.cell(row=row, column=col)
            if cell.alignment is None:
                cell.alignment = Alignment(wrap_text=True)
            else:
                # 保持现有对齐方式，只添加自动换行
                cell.alignment = Alignment(
                    horizontal=cell.alignment.horizontal,
                    vertical=cell.alignment.vertical,
                    wrap_text=True
                )

def adjust_column_widths(ws, df, merged_columns):
    """
    根据数据内容调整列宽
    """
    for col_idx, column in enumerate(df.columns, 1):
        # 计算列的最大内容长度
        max_length = len(str(column))  # 标题长度
        
        for row_idx in range(len(df)):
            cell_value = df.iloc[row_idx, col_idx - 1]
            if pd.notna(cell_value):
                # 计算中文字符长度（中文字符占2个单位宽度）
                content_length = calculate_display_width(str(cell_value))
                max_length = max(max_length, content_length)
        
        # 设置列宽，限制最大宽度
        if col_idx in merged_columns:  # 新增：判断是否为合并列
            column_width = min(max_length + 2, 35)  # 合并列最大宽度35
        else:
            column_width = min(max_length + 2, 50)  # 原最大宽度50
        ws.column_dimensions[ws.cell(row=1, column=col_idx).column_letter].width = column_width

def calculate_display_width(text):
    """
    计算文本显示宽度（中文字符按2个单位计算）
    """
    width = 0
    for char in text:
        if ord(char) > 127:  # 非ASCII字符（包括中文）
            width += 2
        else:
            width += 1
    return width

def set_border_for_all_cells(ws, total_rows, total_cols):
    """
    为所有单元格设置外边框
    """
    # 定义细边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 遍历所有单元格设置边框
    for row in range(1, total_rows + 1):
        for col in range(1, total_cols + 1):
            cell = ws.cell(row=row, column=col)
            cell.border = thin_border  # 新增: 设置四周边框

if __name__ == "__main__":
    process_csv_to_excel('output.csv')
