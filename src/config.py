import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()


# 重新加载环境变量
def re_load():
    load_dotenv()
# 基本配置
def get_DATA_DIR():
    return os.getenv('DATA_DIR', 'data')
def get_THREAD_COUNT():
    return int(os.getenv('THREAD_COUNT', '4'))
def get_BATCH_COUNT():
    return int(os.getenv('BATCH_COUNT', '4'))
# 用于调试的配置
def get_TEST_LEVEL2_NAME():
    return os.getenv('TEST_LEVEL2_NAME', '')

def get_TEST_LEVEL3_NAME():
    return os.getenv('TEST_LEVEL3_NAME', '')

# LLM 模型配置
def get_LLM_PROVIDER():
    return os.getenv('LLM_PROVIDER', 'openai')

# 
def get_LLM_API_BASE():
    if get_LLM_PROVIDER() == 'openai':
        return os.getenv('OPENAI_API_BASE', '')
    elif get_LLM_PROVIDER() == 'siliconflow':
        return "https://api.siliconflow.com/v1"
    elif get_LLM_PROVIDER() == 'dashcope':
        return "https://dashscope.aliyuncs.com/compatible-mode/v1"
    else:
        return ""

def get_LLM_API_KEY():
    if get_LLM_PROVIDER() == 'openai':
        return os.getenv('OPENAI_API_KEY', '')
    elif get_LLM_PROVIDER() == 'siliconflow':
        return os.getenv('SILICONFLOW_API_KEY', '')
    elif get_LLM_PROVIDER() == 'dashcope':
        return os.getenv('DASHSCOPE_API_KEY', '')
    else:
        return ""

def get_LLM_MODEL():
    if get_LLM_PROVIDER() == 'openai':
        return os.getenv('OPENAI_MODEL', '')
    elif get_LLM_PROVIDER() == 'siliconflow':
        return os.getenv('SILICONFLOW_MODEL', 'Qwen/Qwen2-7B-Instruct')
    elif get_LLM_PROVIDER() == 'dashcope':
        return os.getenv('DASHSCOPE_MODEL', 'qwen-turbo')
    else:
        return ""

def get_LLM_Temperature():
    return float(os.getenv('LLM_TEMPERATURE', '0.3'))
# API限制配置
def get_API_QPM():
    return int(os.getenv('API_QPM', '600'))

def get_API_TPM():
    return int(os.getenv('API_TPM', '10000000'))

# 提示词配置
def get_PROMPT_PATH():
    return os.getenv('PROMPT_PATH', 'prompt/prompt.md')

# 嵌入模型配置
def get_EMBEDDING_MODEL():
    return os.getenv('EMBEDDING_MODEL', 'embed')

def get_EMBEDDING_API_BASE():
    return os.getenv('EMBEDDING_API_BASE', 'https://ai.secsign.online:38080')

def get_EMBEDDING_API_KEY():
    return os.getenv('EMBEDDING_API_KEY', '')
# 知识库相关配置
def get_KNOWLEDGE_BASE_ENABLED():
    if os.getenv('KNOWLEDGE_BASE_ENABLED', 'true').lower() == 'false':
        return False
    return True

def get_KNOWLEDGE_BASE_CACHE_DIR():
    return os.getenv('KNOWLEDGE_BASE_CACHE_DIR', '')

def get_MARKDOWN_MANUAL_PATH():
    return os.getenv('MARKDOWN_MANUAL_PATH', "")

def get_SQL_FILE_PATH():
    return os.getenv('SQL_FILE_PATH', "")

def get_KNOWLEDGE_BASE_TOP_K():
    return 5

def get_KNOWLEDGE_BASE_SIMILARITY_THRESHOLD():
    return 0.3

# 分块处理配置
def get_CHUNK_PROCESSING_ENABLED():
    return True

def get_MARKDOWN_CHILD_PATTERN():
    return '####'

def get_SQL_CHUNK_BY_TABLE():
    return True

def get_MAX_CHUNK_SIZE():
    return 20480

def get_MIN_CHUNK_SIZE():
    return 100

def get_EMBEDDING_BATCH_SIZE():
    return 3

# 
def get_OUT_EXCLUDED_FIELDS():
    os.getenv('OUT_EXCLUDED_FIELDS', "").split(',')

# 校验结果输出配置
def get_CHECK_OUTPUT_DIR():
    return get_DATA_DIR() + "/check"

def get_CHECK_INPUT_DEBUG_FILE():
    return "cosmic_validation_input.json"

def get_CHECK_RESULT_FILE():
    return "cosmic_validation_result.json"

def get_CHECK_PROMPT_FILE():
    return "prompt/check_fix_prompt.md"

# 功能需求文档生成配置
def get_DOC_PROMPT_FILE():
    return "prompt/doc_prompt.md"
def get_COLUMN_MAPPING():
    return {
            "level1": "一级功能模块",
            "level2": "二级功能模块",
            "level3": "三级功能模块",
            "function": "功能过程",
            "user": "功能用户",
            "trigger": "触发事件",
            "subprocess": "子过程描述",
            "data_movement": "数据移动类型",
            "data_group": "数据组",
            "data_attribute": "数据属性"
        }
def get_DOC_START_NUMBER():
    return os.getenv('DOC_START_NUMBER', "2")