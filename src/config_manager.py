#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

提供配置文件的读取、写入和管理功能：
1. 从.env文件读取配置
2. 提供WEB界面配置修改
3. 配置验证和默认值处理
"""

import os
import re
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv, set_key, unset_key
import config

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, env_file: str = ".env"):
        """
        初始化配置管理器
        
        Args:
            env_file: 环境变量文件路径
        """
        self.env_file = env_file
        self.env_example_file = ".env.example"
        self.ensure_env_file()
        self.load_config()
    
    def ensure_env_file(self):
        """确保.env文件存在，如果不存在则从.env.example复制"""
        if not os.path.exists(self.env_file) and os.path.exists(self.env_example_file):
            with open(self.env_example_file, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write(content)
    
    def load_config(self):
        """加载配置"""
        load_dotenv(self.env_file, override=True)
    
    def get_config_groups(self) -> Dict[str, Dict[str, Any]]:
        """
        获取分组的配置信息
        
        Returns:
            分组的配置字典
        """
        config_groups = {
            "基本配置": {
                "BATCH_COUNT": {
                    "value": self.get_int("BATCH_COUNT", 30),
                    "type": "number",
                    "description": "每批次最多的三级模块数量",
                    "min": 1,
                    "max": 100
                },
                "PROMPT_PATH": {
                    "value": self.get_str("PROMPT_PATH", "prompt.md"),
                    "type": "text",
                    "description": "提示词文件路径"
                }
            },
            "嵌入模型配置": {
                "EMBEDDING_MODEL": {
                    "value": self.get_str("EMBEDDING_MODEL", "embed"),
                    "type": "text",
                    "description": "嵌入模型名称"
                },
                "EMBEDDING_API_BASE": {
                    "value": self.get_str("EMBEDDING_API_BASE", "https://ai.secsign.online:38080"),
                    "type": "url",
                    "description": "嵌入模型API基础URL"
                },
                "EMBEDDING_API_KEY": {
                    "value": self.get_str("EMBEDDING_API_KEY", ""),
                    "type": "password",
                    "description": "嵌入模型API密钥"
                }
            },
            "大模型配置": {
                "ENDPOINT_URL": {
                    "value": self.get_str("ENDPOINT_URL", "https://ai.secsign.online:3003/v1/chat/completions"),
                    "type": "url",
                    "description": "大模型API端点URL"
                },
                "MODEL_NAME": {
                    "value": self.get_str("MODEL_NAME", "qwen3-32b"),
                    "type": "text",
                    "description": "大模型名称"
                },
                "API_KEY": {
                    "value": self.get_str("API_KEY", ""),
                    "type": "password",
                    "description": "大模型API密钥"
                },
                "API_QPM": {
                    "value": self.get_int("API_QPM", 60),
                    "type": "number",
                    "description": "每分钟调用次数限制",
                    "min": 1,
                    "max": 1000
                },
                "API_TPM": {
                    "value": self.get_int("API_TPM", 100000),
                    "type": "number",
                    "description": "每分钟处理token数量",
                    "min": 1000,
                    "max": 1000000
                }
            },
            "知识库配置": {
                "KNOWLEDGE_BASE_ENABLED": {
                    "value": self.get_bool("KNOWLEDGE_BASE_ENABLED", True),
                    "type": "boolean",
                    "description": "是否启用知识库功能"
                },
                "KNOWLEDGE_BASE_TOP_K": {
                    "value": self.get_int("KNOWLEDGE_BASE_TOP_K", 5),
                    "type": "number",
                    "description": "检索返回的最相关结果数量",
                    "min": 1,
                    "max": 20
                },
                "KNOWLEDGE_BASE_SIMILARITY_THRESHOLD": {
                    "value": self.get_float("KNOWLEDGE_BASE_SIMILARITY_THRESHOLD", 0.1),
                    "type": "number",
                    "description": "相似度阈值",
                    "min": 0.0,
                    "max": 1.0,
                    "step": 0.1
                },
                "MARKDOWN_MANUAL_PATH": {
                    "value": self.get_str("MARKDOWN_MANUAL_PATH", ""),
                    "type": "textarea",
                    "description": "Markdown手册文件路径（多个文件用逗号分隔）"
                },
                "SQL_FILE_PATH": {
                    "value": self.get_str("SQL_FILE_PATH", ""),
                    "type": "textarea",
                    "description": "SQL文件路径（多个文件用逗号分隔）"
                }
            },
            "分块处理配置": {
                "CHUNK_PROCESSING_ENABLED": {
                    "value": self.get_bool("CHUNK_PROCESSING_ENABLED", True),
                    "type": "boolean",
                    "description": "是否启用分块处理"
                },
                "MAX_CHUNK_SIZE": {
                    "value": self.get_int("MAX_CHUNK_SIZE", 20480),
                    "type": "number",
                    "description": "单个分块的最大字符数",
                    "min": 1000,
                    "max": 100000
                },
                "MIN_CHUNK_SIZE": {
                    "value": self.get_int("MIN_CHUNK_SIZE", 100),
                    "type": "number",
                    "description": "单个分块的最小字符数",
                    "min": 50,
                    "max": 1000
                }
            }
        }
        
        return config_groups
    
    def get_str(self, key: str, default: str = "") -> str:
        """获取字符串配置值"""
        return os.getenv(key, default)
    
    def get_int(self, key: str, default: int = 0) -> int:
        """获取整数配置值"""
        try:
            return int(os.getenv(key, str(default)))
        except (ValueError, TypeError):
            return default
    
    def get_float(self, key: str, default: float = 0.0) -> float:
        """获取浮点数配置值"""
        try:
            return float(os.getenv(key, str(default)))
        except (ValueError, TypeError):
            return default
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """获取布尔配置值"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def set_config(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            # 转换值为字符串
            if isinstance(value, bool):
                str_value = 'true' if value else 'false'
            else:
                str_value = str(value)
            
            # 设置到.env文件
            set_key(self.env_file, key, str_value)
            
            # 重新加载配置
            self.load_config()
            
            return True
        except Exception:
            return False
    
    def update_configs(self, configs: Dict[str, Any]) -> Dict[str, bool]:
        """
        批量更新配置
        
        Args:
            configs: 配置字典
            
        Returns:
            更新结果字典
        """
        results = {}
        for key, value in configs.items():
            results[key] = self.set_config(key, value)
        
        return results
    
    def validate_config(self, key: str, value: Any) -> tuple[bool, str]:
        """
        验证配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            (是否有效, 错误信息)
        """
        config_groups = self.get_config_groups()
        
        # 查找配置项
        config_item = None
        for group in config_groups.values():
            if key in group:
                config_item = group[key]
                break
        
        if not config_item:
            return False, "未知的配置项"
        
        config_type = config_item.get("type", "text")
        
        # 类型验证
        if config_type == "number":
            try:
                num_value = float(value) if '.' in str(value) else int(value)
                min_val = config_item.get("min")
                max_val = config_item.get("max")
                
                if min_val is not None and num_value < min_val:
                    return False, f"值不能小于 {min_val}"
                if max_val is not None and num_value > max_val:
                    return False, f"值不能大于 {max_val}"
                    
            except (ValueError, TypeError):
                return False, "必须是数字"
        
        elif config_type == "url":
            if not re.match(r'^https?://', str(value)):
                return False, "必须是有效的URL"
        
        elif config_type == "boolean":
            if str(value).lower() not in ('true', 'false', '1', '0', 'yes', 'no', 'on', 'off'):
                return False, "必须是布尔值"
        
        return True, ""
    
    def reset_to_default(self, key: str) -> bool:
        """
        重置配置为默认值
        
        Args:
            key: 配置键
            
        Returns:
            是否重置成功
        """
        try:
            # 从.env文件中删除该键
            unset_key(self.env_file, key)
            
            # 重新加载配置
            self.load_config()
            
            return True
        except Exception:
            return False
    
    def export_config(self) -> str:
        """
        导出当前配置为.env格式
        
        Returns:
            配置文件内容
        """
        if os.path.exists(self.env_file):
            with open(self.env_file, 'r', encoding='utf-8') as f:
                return f.read()
        return ""
    
    def import_config(self, content: str) -> bool:
        """
        导入配置内容
        
        Args:
            content: 配置文件内容
            
        Returns:
            是否导入成功
        """
        try:
            # 备份当前配置
            backup_file = f"{self.env_file}.backup"
            if os.path.exists(self.env_file):
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    backup_content = f.read()
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(backup_content)
            
            # 写入新配置
            with open(self.env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 重新加载配置
            self.load_config()
            
            return True
        except Exception:
            # 恢复备份
            if os.path.exists(backup_file):
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_content = f.read()
                with open(self.env_file, 'w', encoding='utf-8') as f:
                    f.write(backup_content)
                os.remove(backup_file)
            
            return False
