import os
import requests
from glob import glob
import json
from pathlib import Path

def get_clean_folder_name(folder_path):
    """去除文件夹名中的_output后缀（如果存在）"""
    folder_name = os.path.basename(folder_path)
    if folder_name.endswith('_output'):
        folder_name = folder_name[:-7]  # 去掉最后的'_output'
    return folder_name

def pdf_to_json(folder_path, api_endpoint):
    """
    将PDF转换为JSON并保存到 [源文件夹名]_json 子文件夹
    
    参数:
        folder_path (str): 包含PDF文件的文件夹路径
        api_endpoint (str): Dify工作流的上传端点URL
        
    返回:
        str: 生成的JSON文件夹路径
    """
    # 获取清理后的文件夹名，并创建对应的json文件夹
    clean_name = get_clean_folder_name(folder_path)
    output_dir = os.path.join(os.path.dirname(folder_path), f"{clean_name}_json")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有PDF文件
    pdf_files = glob(os.path.join(folder_path, "*.docx"))
    
    if not pdf_files:
        print(f"在文件夹 {folder_path} 中没有找到文件")
        return None
    
    print(f"找到 {len(pdf_files)} 个PDF文件，开始转换为JSON...")
    
    for pdf_file in pdf_files:
        try:
            # 读取PDF文件
            with open(pdf_file, "rb") as f:
                files = {"file": (os.path.basename(pdf_file), f, "application/docx")}
                
                # 发送POST请求到Dify
                response = requests.post(api_endpoint, files=files)
                
                if response.status_code == 200:
                    # 生成JSON保存路径
                    json_filename = os.path.splitext(os.path.basename(pdf_file))[0] + ".json"
                    json_filepath = os.path.join(output_dir, json_filename)
                    
                    # 保存JSON内容
                    with open(json_filepath, "w", encoding="utf-8") as json_f:
                        json.dump(response.json(), json_f, ensure_ascii=False, indent=2)
                    
                    print(f"JSON已保存到: {json_filepath}")
                else:
                    print(f"转换失败: {pdf_file} - 状态码: {response.status_code}")
                    
        except Exception as e:
            print(f"处理文件 {pdf_file} 时出错: {str(e)}")
    
    return output_dir

def json_to_markdown(input_dir, output_dir):
    """批量处理文件夹中的所有JSON文件并转换为Markdown"""
    if not os.path.exists(input_dir):
        print(f"输入目录不存在: {input_dir}")
        return
    
    print(f"开始JSON到Markdown转换: {input_dir} -> {output_dir}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 遍历输入目录
    for filename in os.listdir(input_dir):
        if filename.endswith('.json'):
            input_path = os.path.join(input_dir, filename)
            try:
                with open(input_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查是否存在 markdown 字段
                if 'markdown' in data:
                    # 直接使用JSON中的内容
                    md_content = data['markdown']

                    # 先将所有###替换为临时占位符
                    md_content = md_content.replace('###', '@@@')
                    # 再将所有##替换为###
                    md_content = md_content.replace('##', '###')
                    # 最后将临时占位符替换为##
                    md_content = md_content.replace('@@@', '##')
                    
                    output_name = f"{Path(filename).stem}.md"  # 去掉converted_前缀
                    output_path = os.path.join(output_dir, output_name)
                    
                    # 写入新文件
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(md_content)
                    print(f"✅ Markdown转换成功: {filename} -> {output_path}")
                else:
                    print(f"⚠️ 跳过: {filename} (无 'markdown' 字段)")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {filename} - {e}")
            except Exception as e:
                print(f"❌ 处理文件 {filename} 时出错: {e}")

def markdown_to_txt(input_dir, output_dir):
    """批量处理文件夹中的所有Markdown文件并转换为TXT"""
    if not os.path.exists(input_dir):
        print(f"输入目录不存在: {input_dir}")
        return
    
    print(f"开始Markdown到TXT转换: {input_dir} -> {output_dir}")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 遍历输入目录
    for filename in os.listdir(input_dir):
        if filename.endswith('.md'):
            input_path = os.path.join(input_dir, filename)
            try:
                with open(input_path, 'r', encoding='utf-8') as f:
                    md_content = f.read()
                
                # 生成txt文件名
                txt_name = f"{Path(filename).stem}.txt"
                txt_path = os.path.join(output_dir, txt_name)
                
                # 写入txt文件
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(md_content)
                print(f"✅ TXT转换成功: {filename} -> {txt_path}")
                
            except Exception as e:
                print(f"❌ 处理文件 {filename} 时出错: {e}")

def process_pdf_to_markdown(pdf_folder, dify_endpoint):
    """
    完整处理流程: PDF -> JSON -> Markdown -> TXT
    """
    # 第一步: PDF转JSON
    json_folder = pdf_to_json(pdf_folder, dify_endpoint)
    if not json_folder:
        return
    
    # 第二步: JSON转Markdown
    clean_name = get_clean_folder_name(pdf_folder)
    markdown_folder = os.path.join(os.path.dirname(pdf_folder), f"{clean_name}_markdown")
    json_to_markdown(json_folder, markdown_folder)
    
    # 第三步: Markdown转TXT
    txt_folder = os.path.join(os.path.dirname(pdf_folder), f"{clean_name}_txt")
    markdown_to_txt(markdown_folder, txt_folder)
    
    print("🎉 全部转换流程完成！")

if __name__ == "__main__":
    # 配置参数
    pdf_folder = "docs/甘肃移动/训练材料"  
    dify_endpoint = "http://10.20.35.250:5005/upload"  # Dify接口地址
    
    # 执行完整流程
    process_pdf_to_markdown(pdf_folder, dify_endpoint)