#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能需求文档生成器 Web UI

提供Web界面用于：
1. 选择xlsx/csv文件
2. 生成markdown文档
3. 在线渲染markdown（支持Mermaid时序图）
4. 重新生成已存在的文档
"""

import os
import glob
import json
import time
from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import markdown
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import html
from doc_generator import RequirementGenerator
from config import REQUIREMENT_GENERATOR_CONFIG
from prompt_manager import PromptManager
from main import make_cosmic
from config_manager import ConfigManager
import config

app = Flask(__name__)
CORS(app)

# 配置
DEBUG_DIR = "debug"
SUPPORTED_EXTENSIONS = ['.xlsx', '.csv']

def get_available_files():
    """获取数据目录下所有支持的文件"""
    files = []
    for ext in SUPPORTED_EXTENSIONS:
        pattern = f"*{ext}"
        found_files = glob.glob(pattern,root_dir=config.DATA_DIR)
        for file_path in found_files:
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'extension': ext,
                'size': os.path.getsize(file_path),
                'modified': os.path.getmtime(file_path)
            }
            
            # 检查对应的markdown文件是否存在
            base_name = os.path.splitext(file_path)[0]
            md_file = f"{base_name}_功能需求文档.md"
            file_info['has_markdown'] = os.path.exists(md_file)
            file_info['markdown_path'] = md_file
            
            files.append(file_info)
    
    # 按修改时间排序
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def generate_flowchart_html(sequence_content):
    """从时序图内容生成流程图HTML"""
    import re

    # 解析时序图内容，提取参与者和交互
    participants = []
    interactions = []
    participant_map = {}

    lines = sequence_content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if line.startswith('participant '):
            # 提取参与者
            match = re.match(r'participant\s+(\w+)\s+as\s+(.+)', line)
            if match:
                participant_id = match.group(1)
                participant_name = match.group(2)
                participants.append({'id': participant_id, 'name': participant_name})
                participant_map[participant_id] = participant_name
        elif any(arrow in line for arrow in ['->', '-->>', '-->']):
            # 提取交互
            arrow_type = 'solid'
            if '->>' in line:
                parts = line.split('->>')
                arrow_type = 'solid'
            elif '-->' in line:
                parts = line.split('-->')
                arrow_type = 'dashed'
            elif '->' in line:
                parts = line.split('->')
                arrow_type = 'solid'
            else:
                continue

            if len(parts) == 2:
                from_actor = parts[0].strip()
                to_and_msg = parts[1].strip()
                if ':' in to_and_msg:
                    to_actor, message = to_and_msg.split(':', 1)
                    to_actor = to_actor.strip()
                    message = message.strip()
                else:
                    to_actor = to_and_msg
                    message = ''

                # 查找参与者在列表中的位置
                from_index = -1
                to_index = -1
                for i, p in enumerate(participants):
                    if p['id'] == from_actor:
                        from_index = i
                    if p['id'] == to_actor:
                        to_index = i

                if from_index >= 0 and to_index >= 0:
                    interactions.append({
                        'from': from_actor,
                        'to': to_actor,
                        'from_index': from_index,
                        'to_index': to_index,
                        'message': message,
                        'type': arrow_type
                    })

    if not participants:
        return '<div class="flowchart-container"><p>无法解析时序图内容</p></div>'

    # 生成流程图HTML
    flowchart_html = '<div class="flowchart-container">'

    # 添加参与者框
    for i, participant in enumerate(participants):
        flowchart_html += f'''
        <div class="participant-box" data-id="{participant['id']}" style="left: {i * 180 + 50}px;">
            {participant['name']}
        </div>
        '''

    # 添加交互箭头和消息
    for i, interaction in enumerate(interactions):
        arrow_class = 'solid-arrow' if interaction['type'] == 'solid' else 'dashed-arrow'

        # 计算箭头位置
        from_x = interaction['from_index'] * 180 + 110  # 参与者框中心
        to_x = interaction['to_index'] * 180 + 110
        y_pos = 120 + i * 60

        # 确定箭头方向
        if from_x < to_x:
            arrow_left = from_x
            arrow_width = to_x - from_x
            arrow_direction = 'right'
        else:
            arrow_left = to_x
            arrow_width = from_x - to_x
            arrow_direction = 'left'

        flowchart_html += f'''
        <div class="interaction" style="top: {y_pos}px; left: {arrow_left}px; width: {arrow_width}px;">
            <div class="arrow {arrow_class} arrow-{arrow_direction}"></div>
            <div class="message">{interaction['message']}</div>
        </div>
        '''

    flowchart_html += '</div>'
    return flowchart_html

def render_markdown_with_mermaid(markdown_content):
    """渲染Markdown内容，支持Mermaid图表和流程图备选方案"""
    # 使用markdown库渲染基本内容
    md = markdown.Markdown(extensions=['codehilite', 'fenced_code', 'tables', 'toc'])
    html_content = md.convert(markdown_content)

    # 查找并处理时序图，添加流程图备选方案
    import re

    def replace_mermaid_with_fallback(match):
        mermaid_content = match.group(1)
        if 'sequenceDiagram' in mermaid_content:
            # 生成流程图备选方案
            flowchart_html = generate_flowchart_html(mermaid_content)
            return f'''
            <div class="diagram-container">
                <div class="mermaid-diagram">
                    <pre class="mermaid">{mermaid_content}</pre>
                </div>
                <div class="fallback-flowchart" style="display: none;">
                    {flowchart_html}
                </div>
                <div class="diagram-toggle">
                    <button onclick="toggleDiagram(this)" class="toggle-btn">切换到流程图</button>
                </div>
            </div>
            '''
        else:
            return f'<pre class="mermaid">{mermaid_content}</pre>'

    # 替换mermaid代码块
    html_content = re.sub(r'<pre><code class="language-mermaid">(.*?)</code></pre>',
                         replace_mermaid_with_fallback, html_content, flags=re.DOTALL)

    # 读取HTML模板文件
    template_path = os.path.join('templates', 'document_template.html')
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            html_template = f.read()

        # 替换模板中的占位符
        html_template = html_template.replace('{{html_content}}', html_content)

    except FileNotFoundError:
        # 如果模板文件不存在，使用简单的HTML包装
        html_template = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>功能需求文档</title>
            <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
        </head>
        <body>
            <div class="container">
                {html_content}
            </div>
            <script>
                mermaid.initialize({{
                    startOnLoad: true,
                    theme: 'default',
                    securityLevel: 'loose'
                }});
            </script>
        </body>
        </html>
        """

    return html_template

@app.route('/')
def index():
    """主页面"""
    files = get_available_files()
    return render_template('index.html', files=files)

@app.route('/api/files')
def api_files():
    """获取文件列表API"""
    files = get_available_files()
    return jsonify(files)

@app.route('/api/generate', methods=['POST'])
def api_generate():
    """生成markdown文档API"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        force_regenerate = data.get('force_regenerate', False)
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 400
        
        # 检查markdown文件是否已存在
        base_name = os.path.splitext(file_path)[0]
        md_file = f"{base_name}_功能需求文档.md"
        
        if os.path.exists(md_file) and not force_regenerate:
            return jsonify({
                'success': True,
                'message': 'Markdown文件已存在',
                'markdown_path': md_file,
                'already_exists': True
            })
        
        # 创建生成器并生成文档
        generator = RequirementGenerator()
        output_file = generator.generate_requirements_document(file_path, md_file)
        
        if output_file:
            return jsonify({
                'success': True,
                'message': '文档生成成功',
                'markdown_path': output_file,
                'already_exists': False
            })
        else:
            return jsonify({'error': '文档生成失败'}), 500
            
    except Exception as e:
        return jsonify({'error': f'生成失败: {str(e)}'}), 500

@app.route('/api/render/<path:filename>')
def api_render(filename):
    """渲染markdown文件API"""
    try:
        if not os.path.exists(filename):
            return jsonify({'error': '文件不存在'}), 404
        
        with open(filename, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        html_content = render_markdown_with_mermaid(markdown_content)
        return html_content
        
    except Exception as e:
        return jsonify({'error': f'渲染失败: {str(e)}'}), 500

@app.route('/download/<path:filename>')
def download_file(filename):
    """下载文件"""
    try:
        if os.path.exists(filename):
            return send_file(filename, as_attachment=True)
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

# 提示词管理相关API
@app.route('/api/prompts')
def api_prompts():
    """获取提示词列表API"""
    try:
        prompt_manager = PromptManager()
        prompt_types = prompt_manager.get_prompt_types()

        result = {}
        for prompt_type in prompt_types:
            history = prompt_manager.get_version_history(prompt_type)
            result[prompt_type] = {
                'latest_version': prompt_manager.get_latest_version(prompt_type),
                'history': history
            }

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'获取提示词列表失败: {str(e)}'}), 500

@app.route('/api/prompts/<prompt_type>')
def api_get_prompt(prompt_type):
    """获取指定类型的提示词内容"""
    try:
        prompt_manager = PromptManager()
        version = request.args.get('version', type=int)
        content = prompt_manager.load_prompt(prompt_type, version)

        return jsonify({
            'success': True,
            'content': content,
            'version': version or prompt_manager.get_latest_version(prompt_type)
        })
    except Exception as e:
        return jsonify({'error': f'获取提示词失败: {str(e)}'}), 500

@app.route('/api/prompts/<prompt_type>', methods=['POST'])
def api_save_prompt(prompt_type):
    """保存提示词内容"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        create_new_version = data.get('create_new_version', True)

        prompt_manager = PromptManager()
        file_path = prompt_manager.save_prompt(prompt_type, content, create_new_version)

        return jsonify({
            'success': True,
            'message': '提示词保存成功',
            'file_path': file_path,
            'version': prompt_manager.get_latest_version(prompt_type)
        })
    except Exception as e:
        return jsonify({'error': f'保存提示词失败: {str(e)}'}), 500

# 功能拆解相关API
@app.route('/api/cosmic/decompose', methods=['POST'])
def api_cosmic_decompose():
    """功能拆解API"""
    try:
        data = request.get_json()
        excel_file = data.get('excel_file')
        prompt_type = data.get('prompt_type', '功能拆解')
        prompt_version = data.get('prompt_version')

        if not excel_file or not os.path.exists(excel_file):
            return jsonify({'error': '文件不存在'}), 400

        # 生成输出文件名
        base_name = os.path.splitext(excel_file)[0]
        output_file = f"{base_name}_cosmic拆解.csv"

        # 调用make_cosmic方法
        result = make_cosmic(excel_file, output_file)

        if result and os.path.exists(output_file):
            return jsonify({
                'success': True,
                'message': 'COSMIC功能拆解完成',
                'output_file': output_file
            })
        else:
            return jsonify({'error': 'COSMIC功能拆解失败'}), 500

    except Exception as e:
        return jsonify({'error': f'功能拆解失败: {str(e)}'}), 500

# 文档生成相关API
@app.route('/api/document/generate', methods=['POST'])
def api_document_generate():
    """文档生成API"""
    try:
        data = request.get_json()
        excel_file = data.get('excel_file')
        prompt_type = data.get('prompt_type', '文档生成')
        prompt_version = data.get('prompt_version')
        force_regenerate = data.get('force_regenerate', False)

        if not excel_file or not os.path.exists(excel_file):
            return jsonify({'error': '文件不存在'}), 400

        # 生成输出文件名
        base_name = os.path.splitext(excel_file)[0]
        md_file = f"{base_name}_功能需求文档.md"

        # 检查markdown文件是否已存在
        if os.path.exists(md_file) and not force_regenerate:
            return jsonify({
                'success': True,
                'message': 'Markdown文件已存在',
                'markdown_path': md_file,
                'already_exists': True
            })

        # 创建生成器并生成文档
        generator = RequirementGenerator()
        output_file = generator.generate_requirements_document(excel_file, md_file)

        if output_file:
            return jsonify({
                'success': True,
                'message': '文档生成成功',
                'markdown_path': output_file,
                'already_exists': False
            })
        else:
            return jsonify({'error': '文档生成失败'}), 500

    except Exception as e:
        return jsonify({'error': f'文档生成失败: {str(e)}'}), 500

# 提示词编辑器相关API
@app.route('/api/editor/prompt/<prompt_type>')
def api_editor_get_prompt(prompt_type):
    """获取提示词内容用于编辑"""
    try:
        prompt_manager = PromptManager()
        version = request.args.get('version', type=int)
        content = prompt_manager.load_prompt(prompt_type, version)
        current_version = version or prompt_manager.get_latest_version(prompt_type)

        return jsonify({
            'success': True,
            'content': content,
            'version': current_version,
            'history': prompt_manager.get_version_history(prompt_type)
        })
    except Exception as e:
        return jsonify({'error': f'获取提示词失败: {str(e)}'}), 500

@app.route('/api/editor/prompt/<prompt_type>', methods=['POST'])
def api_editor_save_prompt(prompt_type):
    """保存编辑后的提示词"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        create_new_version = data.get('create_new_version', True)

        prompt_manager = PromptManager()
        file_path = prompt_manager.save_prompt(prompt_type, content, create_new_version)
        new_version = prompt_manager.get_latest_version(prompt_type)

        return jsonify({
            'success': True,
            'message': f'提示词保存成功，版本号: {new_version}',
            'file_path': file_path,
            'version': new_version,
            'history': prompt_manager.get_version_history(prompt_type)
        })
    except Exception as e:
        return jsonify({'error': f'保存提示词失败: {str(e)}'}), 500

@app.route('/api/editor/prompt/<prompt_type>/<int:version>', methods=['DELETE'])
def api_editor_delete_prompt_version(prompt_type, version):
    """删除指定版本的提示词"""
    try:
        prompt_manager = PromptManager()
        success = prompt_manager.delete_version(prompt_type, version)

        if success:
            return jsonify({
                'success': True,
                'message': f'版本 {version} 删除成功',
                'history': prompt_manager.get_version_history(prompt_type)
            })
        else:
            return jsonify({'error': '删除失败，文件不存在'}), 404

    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

# 系统配置管理相关API
@app.route('/api/config')
def api_get_config():
    """获取系统配置"""
    try:
        config_manager = ConfigManager()
        config_groups = config_manager.get_config_groups()

        return jsonify({
            'success': True,
            'config_groups': config_groups
        })
    except Exception as e:
        return jsonify({'error': f'获取配置失败: {str(e)}'}), 500

@app.route('/api/config', methods=['POST'])
def api_update_config():
    """更新系统配置"""
    try:
        data = request.get_json()
        configs = data.get('configs', {})

        config_manager = ConfigManager()

        # 验证配置
        validation_errors = {}
        for key, value in configs.items():
            is_valid, error_msg = config_manager.validate_config(key, value)
            if not is_valid:
                validation_errors[key] = error_msg

        if validation_errors:
            return jsonify({
                'error': '配置验证失败',
                'validation_errors': validation_errors
            }), 400

        # 更新配置
        results = config_manager.update_configs(configs)

        # 检查是否有更新失败的配置
        failed_configs = [key for key, success in results.items() if not success]

        if failed_configs:
            return jsonify({
                'error': f'以下配置更新失败: {", ".join(failed_configs)}',
                'results': results
            }), 500

        return jsonify({
            'success': True,
            'message': '配置更新成功',
            'results': results
        })

    except Exception as e:
        return jsonify({'error': f'更新配置失败: {str(e)}'}), 500

@app.route('/api/config/export')
def api_export_config():
    """导出配置"""
    try:
        config_manager = ConfigManager()
        content = config_manager.export_config()

        return jsonify({
            'success': True,
            'content': content
        })
    except Exception as e:
        return jsonify({'error': f'导出配置失败: {str(e)}'}), 500

@app.route('/api/config/import', methods=['POST'])
def api_import_config():
    """导入配置"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        config_manager = ConfigManager()
        success = config_manager.import_config(content)

        if success:
            return jsonify({
                'success': True,
                'message': '配置导入成功'
            })
        else:
            return jsonify({'error': '配置导入失败'}), 500

    except Exception as e:
        return jsonify({'error': f'导入配置失败: {str(e)}'}), 500

@app.route('/api/config/<key>/reset', methods=['POST'])
def api_reset_config(key):
    """重置单个配置为默认值"""
    try:
        config_manager = ConfigManager()
        success = config_manager.reset_to_default(key)

        if success:
            return jsonify({
                'success': True,
                'message': f'配置 {key} 已重置为默认值'
            })
        else:
            return jsonify({'error': '重置失败'}), 500

    except Exception as e:
        return jsonify({'error': f'重置配置失败: {str(e)}'}), 500

if __name__ == '__main__':
    # 确保debug目录存在
    os.makedirs(DEBUG_DIR, exist_ok=True)
    
    # 启动应用
    print("=== COSMIC功能需求文档生成器 Web UI ===")
    print("访问地址: http://localhost:5000")
    print("支持的文件格式: .xlsx, .csv")
    print("功能:")
    print("  1. 选择数据文件")
    print("  2. 生成功能需求文档")
    print("  3. 在线渲染Markdown（支持Mermaid时序图）")
    print("  4. 重新生成已存在的文档")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5001)
