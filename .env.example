# COSMIC功能拆解系统配置文件
# 复制此文件为.env并修改相应配置

# 基本配置
BATCH_COUNT=30
DATA_DIR=data/
THREAD_COUNT=4
MAX_THREAD_COUNT=4

# 提示词配置
PROMPT_PATH=prompt/prompt.md

# 嵌入模型配置
EMBEDDING_MODEL=embed
EMBEDDING_API_BASE=https://ai.secsign.online:38080
EMBEDDING_API_KEY=sk-your-embedding-api-key

# 测试配置
TEST_LEVEL2_NAME=证书下发
TEST_LEVEL3_NAME=

# LLM Provider配置 (openai/dashscope/siliconflow)
LLM_PROVIDER=openai

# OpenAI兼容配置
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
OPENAI_API_KEY=sk-your-openai-api-key

# 阿里云百炼配置
DASHSCOPE_MODEL=qwen-turbo
DASHSCOPE_API_KEY=sk-your-dashscope-api-key

# 硅基流动配置
SILICONFLOW_MODEL=Qwen/Qwen2-7B-Instruct
SILICONFLOW_API_KEY=sk-your-siliconflow-api-key

# 兼容旧配置（将根据LLM_PROVIDER自动设置）
ENDPOINT_URL=
MODEL_NAME=
API_KEY=

# API限制配置
API_QPM=600
API_TPM=10000000

# 校验配置
CHECK_BATCH_COUNT=100
CHECK_EXCLUDED_FIELDS=预估工作量（人天）,功能描述
CHECK_OUTPUT_DIR=debug
