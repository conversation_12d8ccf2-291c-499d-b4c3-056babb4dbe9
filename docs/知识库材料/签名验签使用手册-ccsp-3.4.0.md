签名验签用户手册

三未信安科技股份有限公司   www.sansec.com.cn

# 版权声明

版权所有 $\circledcirc$ 三未信安科技股份有限公司 2024 保留一切权利（包括但不限于修订、最终解释权）。

本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制、仿制、备份和修改，并不得以任何形式传播。

# 特别提示

由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本文档而造成的直接和间接损失或损害负责。

# 联系我们

感谢您使用我们的产品，如果您对我们的产品有什么意见和建议，可以通过电话、传真或电子邮件等方式向我们反馈。

电话：+86-10-5978 5977  
传真：+86-10-5978 5937  
邮箱：<EMAIL>  
地址：北京市朝阳区广顺北大街16 号院2 号楼华彩大厦16 层[100102]网址：www.sansec.com.cn  
各地分公司与办事处地址请前往官方网站查阅。

# 目录

1. 功能模块介绍.1.1 签名验签.1.1.1 用户证书.1.1.2 应用证书. 51.1.3 信任域管理.  
公司介绍. 11

### 外部公开

# 1. 功能模块介绍

### 1.1 签名验签

## 1.1.1 用户证书

### 1.1.1.1 导入用户证书

点击用户证书列表上方的“导入证书”按钮，输入证书标签、选择证书算法、上传签名证书、上传加密证书（加密证书非必填），点击“提交”，导入用户证书。

![](images/1703278bf193ba5b1b6c924682944ac5d4797a776da1ad5b8b25ed437860b84e.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示用户证书管理界面的截图。界面上方有一个标签栏，当前选中的标签是“用户证书”。在标签栏下方，有一个搜索区域，包含两个输入框和一个下拉菜单。第一个输入框用于输入证书标签，第二个输入框用于选择证书状态，右侧有一个蓝色的“查询”按钮和一个灰色的“重置”按钮。

在搜索区域下方，有一个表格，表头包括“证书标签”、“签名序列号”、“加密序列号”、“证书算法”、“证书状态”和“操作”六个列。表格中目前没有数据，显示“暂无数据”。

在表格上方左侧，有一个绿色的“导入证书”按钮，旁边有一个圆形图标，表示可以导入新的证书。

在表格下方右侧，有一个分页导航栏，显示当前页面为第1页，共0条记录，每页显示20条记录。
```


![](images/7eba969bf09da9681e594eafbbdc1f4381910e2a42a1f4cd035d1a8950606217.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个用户证书导入界面，界面主要分为两个部分：左侧的导航栏和右侧的内容区域。

在左侧的导航栏中，有两个选项：“证书标签”和“导入证书”，当前选中的选项是“导入证书”。

在右侧的内容区域中，有一个弹出的窗口，标题为“导入证书”。在这个窗口中有四个输入项：

1. 证书标签：这是一个必填项，需要用户输入证书的标签。
2. 证书算法：这是一个下拉菜单，当前选择的是“SM2”。
3. 签名证书：这是一个文件上传按钮，用户可以点击上传签名证书。
4. 加密证书：这也是一个文件上传按钮，用户可以点击上传加密证书。

在这些输入项下方，有两个按钮：“取消”和“提交”。用户可以点击“取消”按钮来取消操作，或者点击“提交”按钮来提交填写的信息。

此外，在窗口的右上角还有一个关闭按钮（X），用户可以点击这个按钮来关闭这个弹出窗口。
```


## 1.1.2 应用证书

### 1.1.2.1 创建应用证书

点击应用证书列表上方的“创建证书”按钮，输入证书标签、通用名、组织名等相关信息，点击“提交”，创建应用证书。

![](images/ea333a0b8a7bde73415ee159371628121101a25206190201fb55355e8b772f95.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示证书管理界面的截图，界面上有多个输入框和按钮。
```


创建之后的证书状态是“申请中”。

### 1.1.2.2 下载证书请求

点击“下载”，可以将P10 请求下载到本地。

### 1.1.2.3 安装证书

拿到P10 请求后，通过外部的CA 签发证书后，点击“安装证书”，将CA 签发的签名证书上传到平台。如果CA 同时签发了加密证书，则可以将加密证书和加密私钥上传到平台。

![](images/732f193ea94c02114beff323f555fe988ddc19d455d10a027f5e90998fabe55a.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示“安装证书”界面的截图。界面上有四个主要部分：

1. **证书标签**：显示为“SSSS”。
2. **签名证书**：有一个输入框，内有提示文字“点击上传”，表示用户可以点击此处上传签名证书文件。
3. **加密证书**：同样有一个输入框，内有提示文字“点击上传”，表示用户可以点击此处上传加密证书文件。当前这个输入框被蓝色边框包围，可能表示它正处于选中状态。
4. **加密私钥**：也有一个输入框，内有提示文字“点击上传”，表示用户可以点击此处上传加密私钥文件。

在这些输入框下方，有两个按钮：“取消”和“提交”。用户可以选择取消操作或提交已上传的证书信息。整个界面简洁明了，旨在引导用户完成证书的安装过程。
```


安装后的证书状态为“使用中”。

### 1.1.2.4 导入应用证书

点击应用证书列表上方的“导入证书”按钮，输入证书标签、选择证书算法、上传签名证书、输入签名证书口令、上传加密证书、输入加密证书口令（加密证书非必填），点击“提交”，导入应用证书。

![](images/3fce0e3d3079f8448274378b09b2630fc646a7dcf56ee59c29cf26536a509358.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张显示证书导入界面的截图。界面上有多个输入框和选项，用于填写和选择证书的相关信息。具体包括：

1. **证书标签**：输入框中已填写“sm2”。
2. **证书算法**：下拉菜单中选择了“SM2”。
3. **签名证书**：有一个“点击上传”的按钮，下方显示已上传文件名为“rsa002-sign.pfx”，并且有一个绿色的对勾表示上传成功。
4. **证书口令**：输入框中已填写了口令，但具体内容被隐藏显示为“......”。
5. **加密证书**：有一个“点击上传”的按钮，下方显示已上传文件名为“rsa002-enc.cer”，并且有一个绿色的对勾表示上传成功。
6. **加密私钥**：有一个“点击上传”的按钮，下方显示已上传文件名为“rsa002-enc.prikey”，并且有一个绿色的对勾表示上传成功。

在界面底部有两个按钮：“取消”和“提交”。用户可以点击“提交”按钮来完成证书的导入操作，或者点击“取消”按钮来放弃当前操作。
```


### 1.1.2.5 停用应用证书

点击应用证书列表对象右侧操作列中“停用”按钮，停用应用证书。

![](images/ec4ae55d97a8304c7e74c02bbc91b6201240adc95d3182de0b5afb198178a84c.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个证书管理界面，界面上有一个弹出的提示框。提示框的内容是“确定要停用该证书吗？”，并且有两个按钮选项：“取消”和“确定”。背景中可以看到一个表格，列出了多个证书的信息，包括证书标签、签名序列号、加密序列号、证书算法、证书状态以及操作选项。每个证书的状态都显示为“使用中”，并且每个证书后面都有“停用”和“删除”的操作按钮。当前选中的证书是“rsa003”，其签名序列号为“499ff329a253791b0042607bf...”，加密序列号为空，证书算法为“RSA”。
```


### 1.1.2.6 删除应用证书

点击应用证书列表对象右侧操作列中“删除”按钮，删除应用证书。

签名验签用户手册

![](images/550434d984040f3af76ff3dbf82b4bc7b094d07e837fea1747e2ee2d96a96504.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个应用程序界面，主要用于管理和操作证书。界面上方有一个标签栏，当前选中的标签是“应用证书”。在标签栏下方，有三个输入框和两个下拉菜单，分别用于输入证书标签、选择证书状态和证书算法，以及两个按钮用于查询和重置。

在界面的中部，有一个表格列出了多个证书的信息，包括证书标签、签名序列号、加密序列号、证书算法和证书状态。每个证书条目后面都有“停用”和“删除”两个操作按钮。

在界面的右下角，有一个弹出的提示框，内容为“确定要删除该证书吗？”，并有两个按钮供用户选择，分别是“取消”和“确定”。

从图片中可以看出，用户正在尝试删除一个证书，并且系统弹出了确认提示框以确保用户确实想要执行此操作。
```


## 1.1.3 信任域管理

### 1.1.3.1 信任域列表

点击信任域管理可以进入信任域俩表页面，展示添加的信任域证书文件的证书标签、序列号、签名算法、颁发者信息、使用者信息、是否进行证书验证、起始时间、过期时间以及是否允许过期。

询序号 证书标签 序列号 签名算法 颁发者 使用者 起始时间 过期时间

### 1.1.3.2 添加信任域

点击信任域管理页面页面的新建按钮，可以添加信任域，输入证书标签，上传文件，选择验证方式，设置是否允许证书过期，点击确定后，成功添加信任域。

![](images/6ab1123a3e5ebf397794f33d04c8d8da3f543e58c65da345b0d4bd42c8a481dc.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这是一张关于添加信任域的界面截图，主要包含以下内容：

1. **证书标签**：有一个输入框，提示用户请输入证书标签。
2. **信任域证书**：有一个蓝色按钮，标有“点击上传”，用于上传信任域证书。下方有一行红色提示文字，说明仅允许上传cer或p7b格式证书文件，且不能超过20KB。
3. **验证方式**：提供了四种选择，分别是不验证、CA验证、CRL验证和OCSP验证，用户可以通过单选按钮进行选择。
4. **允许证书过期**：有两个选项，分别是不允许和允许，当前选中的是“不允许”。
5. **底部操作按钮**：有两个按钮，一个是灰色的“取消”按钮，另一个是蓝色的“确定”按钮，用于确认或取消操作。

整个界面设计简洁明了，方便用户进行相关设置。
```
  
签名验签用户手册

### 1.1.3.3 编辑信任域

在信任域管理列表页面，点击右侧操作列的编辑按钮，弹出编辑信任域页面，可以编辑信任域的验证方式，设置是否允许证书过期。

![](images/ccf36722627ca546e8084321b509757d8dd1e46c2126331b8f7eefe58ce115bf.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示了一个编辑信任域的界面，具体包含以下内容：

1. **证书标签**：输入框中填写了“SM2-GQ-n-sign”。
2. **验证方式**：有四个选项可供选择，分别是“不验证”、“CA验证”、“CRL验证”和“OCSP验证”。当前选中的是“不验证”。
3. **允许证书过期**：有两个选项，分别是“不允许”和“允许”。当前选中的是“允许”。

在界面底部有两个按钮，分别是“取消”和“确定”，用户可以通过点击这两个按钮来取消操作或确认设置。
```
  
图 1-3-2 添加信任域  
图 1-3-3 编辑信任域

### 1.1.3.4 删除信任域

在信任域管理列表页面，点击右侧操作列的删除按钮，可以删除信任域证书文件和记录。

签名验签用户手册

![](images/b7cd1d1e4567419805db52bdb806a3806a5978eeb3e9f1e3829c11fc28bff2b8.jpg)
```text
text文本框内的内容是视觉模型对图片的识别结果：
这张图片显示的是一个证书管理系统的界面，具体来说是一个信任域管理页面。界面上列出了多个证书的详细信息，包括证书标签、序列号、签名算法、颁发者、使用者、证书验证状态、起始时间、过期时间以及是否允许过期等信息。

当前页面上有一个弹出的提示框，询问用户是否确认删除证书标签为“rsa002”的信任域证书。这个提示框提供了“取消”和“确定”两个选项供用户选择。

从列表中可以看到，证书标签为“rsa002”的证书使用了SHA256WITHRSA的签名算法，由C=CN,CN=rsa002颁发并使用，证书尚未经过验证，起始时间为2023-05-29 09:17:54，过期时间为2037-05-17 09:17:54，并且允许过期。

此外，页面顶部显示了当前操作员的信息和许可证的有效期，底部则有分页导航栏，显示当前是第一页，共有27条记录，每页显示20条记录。
```
  
图 1-3-4 删除信任域

# 公司介绍

三未信安科技股份有限公司（股票代码：688489）成立于2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。

三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码芯片、PCI-E 接口密码卡、服务器密码机、金融数据密码机、签名验签服务器、云密码机、数据库加密机、SSL VPN、IPSec VPN、密钥管理系统、密码服务平台、身份认证系统等，全面支持 SM1、SM2、SM3、SM4、SM7、SM9、ZUC 等国产密码算法和RSA、ECC、AES、SHA 等国际密码算法，为关键信息基础设施和重要信息系统提供安全的密码运算和完善的密钥管理机制。三未信安在云计算、大数据、物联网、车联网、人工智能、区块链、隐私保护计算、数字货币等新兴技术领域进行了积极的技术创新，并推出了一系列新型密码解决方案。

经过十几年的市场开拓，三未信安的产品和服务赢得了客户和市场的认可，产品已广泛应用于金融、证券、能源、电信、交通、电子商务等行业，以及海关、公安、税务、水利、医疗保障等政府部门。

三未信安是国家级高新技术企业、国家级专精特新重点“小巨人”企业，公司研发了国内首款安全三级密码板卡和首款安全三级密码机，公司的密码机通过了 FIPS 140-2Level3（美国联邦信息处理标准 3 级）认证，荣获五次国家密码科技进步奖。公司是全国信息安全标准化技术委员会和密码行业标准化技术委员会成员单位，牵头和参与制定了二十余项密码领域国家标准或行业标准。

三未信安坚持 “做客户信赖的公司，做有核心技术的公司，做员工热爱的公司”的发展理念，恪守“让生活更美好，做对社会真正有价值的事情”的价值追求，以“用密码技术守护数字世界”为使命，凝聚人才、锐意进取，立志为我国的网络信息安全事业贡献自己的力量！