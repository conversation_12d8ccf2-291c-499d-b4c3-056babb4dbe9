密码软算法产品用户手册

# 版权声明

版权所有 $\circledcirc$ 三未信安科技股份有限公司 2024 保留一切权利（包括但不限于修订、最终解释权）。

本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制、仿制、备份和修改，并不得以任何形式传播。

# 特别提示

由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本文档而造成的直接和间接损失或损害负责。

# 联系我们

感谢您使用我们的产品，如果您对我们的产品有什么意见和建议，可以通过电话、传真或电子邮件等方式向我们反馈。

电话：400-00-90196  
邮箱：<EMAIL>  
网址：www.sansec.com.cn  
公司地址、各地分公司与办事处地址请前往官方网站查阅。

# 目录

1. 产品简介. 1  
2. 快速使用指南.  
3. 功能模块介绍.  
4. 常见问题.. 2  
4.1 解密返回 0x05 错误.  
公司介绍..

### 外部公开

# 1. 产品简介

密码软算法模块支持 RSA/SHA/AES/3DES 等国际算法以及 SM2/SM3/SM4 等国密算法，可运行在相对安全环境下，密钥的安全由使用者自行保证的场景。

另外支持白盒算法，白盒密码算法模块支持 RSA/AES 等国际算法以及SM2/SM4 等国密算法。可运行在终端设备，或需要自行存储非明文密钥文件的场景。白盒密码算法，将原始密钥导出为混淆加固的白盒密钥，从而可落成文件存储到设备本地，该混淆密钥文件只能通过白盒算法库调用，才能实现其作为密钥的原始功能，如，对于对称算法而言是加解密，对于非对称算法而言是签名或解密等私钥运算（公钥运算无混淆保护）。

同时，白盒算法支持传入设备信息以实现设备信息与白盒密钥的绑定。当白盒密钥绑定了某台设备上获取的设备信息时，即便将白盒密钥与白盒算法库同时拷贝到另一台设备上使用，若另一台设备上获取输入的信息不同，也将无法实现密钥正常的功能，如，对称算法加解密得到错误的结果，非对称算法签名或解密等运算得到错误结果。

# 2. 快速使用指南

产品以动态库或静态库的形式提供集成对接。动态库的情况下，只需将库文件拷贝至系统库路径下，并在程序编译时标明引用；静态库则由开发人员直接引用编译即可。

# 3. 功能模块介绍

具体包含所有基础密码功能如下：SM2/SM3/SM4/；  
RSA1024/2048/4096/SHA1/256/384/521/；  
AES128/192/256/3DES128/192；  
SM3-HMAC /SHA1/256/384/512-HMAC/；  
白盒算法 WBSM2 WBRSA1024/2048 WBAES128/256 WBSM4 。  
其中对称算法包含不同分组模式包括：SM4-ECB/CBC/CTR/GCM/CCM/OFB/CFB/XTS/BC/OCB；  
AES128/192/256 -ECB/CBC/CTR/GCM/CCM/OFB/CFB/XTS/BC/OCB；  
WBSM4-ECB/CBC/CTR/OFB；  
WBAES256-ECB/CBC/CTR/OFB；  
3DES16/24-ECB/CBC/CTR/OFB/CFB。

# 4. 常见问题

### 1.1 解密返回0x05 错误

故障描述：调用解密接口对数据进行加密，报0X05 错误

<html><body><table><tr><td>可能的原因</td><td>排查方法</td></tr><tr><td>输出缓冲区空间过小</td><td>输出缓冲区长度至少等于输入数据长度。</td></tr></table></body></html>

公司介绍

三未信安科技股份有限公司（股票代码：688489）成立于2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。

三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码芯片、PCI-E 接口密码卡、服务器密码机、金融数据密码机、签名验签服务器、云密码机、数据库加密机、SSL VPN、IPSec VPN、密钥管理系统、密码服务平台、身份认证系统等，全面支持 SM1、SM2、SM3、SM4、SM7、SM9、ZUC 等国产密码算法和RSA、ECC、AES、SHA 等国际密码算法，为关键信息基础设施和重要信息系统提供安全的密码运算和完善的密钥管理机制。三未信安在云计算、大数据、物联网、车联网、人工智能、区块链、隐私保护计算、数字货币等新兴技术领域进行了积极的技术创新，并推出了一系列新型密码解决方案。

经过十几年的市场开拓，三未信安的产品和服务赢得了客户和市场的认可，产品已广泛应用于金融、证券、能源、电信、交通、电子商务等行业，以及海关、公安、税务、水利、医疗保障等政府部门。

三未信安是国家级高新技术企业、国家级专精特新重点“小巨人”企业，公司研发了国内首款安全三级密码板卡和首款安全三级密码机，公司的密码机通过了 FIPS 140-2Level3（美国联邦信息处理标准 3 级）认证，荣获五次国家密码科技进步奖。公司是全国信息安全标准化技术委员会和密码行业标准化技术委员会成员单位，牵头和参与制定了二十余项密码领域国家标准或行业标准。

三未信安坚持“做客户信赖的公司，做有核心技术的公司，做员工热爱的公司”的发展理念，恪守“让生活更美好，做对社会真正有价值的事情”的价值追求，以“用密码技术守护数字世界”为使命，凝聚人才、锐意进取，立志为我国的网络信息安全事业贡献自己的力量！