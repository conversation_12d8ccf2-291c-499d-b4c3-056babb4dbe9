数字证书认证系统用户手册

# 版权声明

版权所有 ©三未信安科技股份有限公司 2025 保留一切权利（包括但不限于修订、最终解释权）。

本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制、仿制、备份和修改，并不得以任何形式传播。

# 特别提示

由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本文档而造成的直接和间接损失或损害负责。

# 联系我们

感谢您使用我们的产品，如果您对我们的产品有什么意见和建议，可以通过电话、传真或电子邮件等方式向我们反馈。

电话：+86-10-5978 5977  
传真：+86-10-5978 5937  
邮箱：<EMAIL>  
网址：www.sansec.com.cn  
公司地址、各地分公司与办事处地址请前往官方网站查阅。

# 目录

1. 产品介绍.  
2. 管理控制台2.1 CRL 配置.2.2 CRL 列表.2.3 证书申请.2.3.1 设备证书申请..2.3.2 用户证书申请.. 42.4 证书管理. 62.4.1 证书更新申请. 62.4.2 证书注销申请. 82.4.3 证书冻结申请. 92.4.4 证书解冻申请. .112.4.5 证书延期申请.. 122.5 证书下载.. .142.5.1 有效证书下载.. .142.5.2 注销证书下载. 162.5.3 过期证书下载.. 17

2.6 机构管理. 18

2.6.1 CA 证书管理. 18  
2.6.2 RA 管理. 23  
2.7 证书模板管理. 26  
2.7.1 证书模板添加. 26  
2.7.2 自定义扩展管理. 28  
2.8 证书扩展管理. .29  
2.8.1 证书扩展添加. 29  
2.8.2 证书扩展管理. 30  
2.8.3 证书模板列表. 32  
2.9 资源信息. 32

公司介绍. 33

## 外部公开

## 1. 产品介绍

SZT1701 数字证书认证系统是由三未信安科技股份有限公司推出的安全基础类产品，为电子商务、电子政务及企业信息化提供数字证书管理等基础安全服务。该系统结合了国内外同类产品的特点，支持SM2、RSA、双证书、双密钥，证书格式符合被广泛接受的X.509 V3 证书标准。该系统支持经国家密码管理局鉴定通过的密码算法和加密设备，可用于数字证书的申请、审核、签发、注销、更新、查询的综合管理。

## 2. 管理控制台

### 2.1 CRL 配置

![](images/c372fbd5264e1f84fe5b678b6e85019682dce06019f4dfb3f61249f86a905cbf.jpg)
```text
这是一张显示密码服务管理平台界面的截图，具体是在进行CRL（证书撤销列表）配置。左侧有一个导航栏，列出了多个功能选项，包括数据加解密、签名验签、密钥管理、文件加密、数据库加密、时间戳、协同签名、动态令牌、电子签章、SSLVPN加密通道、数字证书认证、CRL配置、CRL列表、证书申请、证书管理、证书下载、机构管理、证书模板和资源信息等。当前选中的功能是“CRL配置”。

在右侧的主内容区域，显示了CRL配置的具体设置项：
- 自动滚动：有“是”和“否”两个选项，当前选择的是“否”。
- 类型：下拉菜单中选择了“增量&全量”。
- 生成周期（小时）：输入框中填写了“1”，表示每1小时生成一次CRL。
- CRL HTTP分发点：有一个“请输入”的提示，表示需要输入HTTP分发点的URL。
- CRL LDAP发布点：同样有一个“请输入”的提示，表示需要输入LDAP发布点的URL。

页面底部有两个按钮：“提交”和“重置”，分别用于保存当前配置或恢复默认设置。右上角显示了许可证有效期为2030年11月1日，并且当前操作员为“oper”。
```


输入各项信息点击提交配置CRL 策略。

注意：1、本服务没有LDAP 第三方服务配置，所以配置LDAP 发布点是并不能发布成功2、HTTP 发布点由于服务部署问题，无法直接请求到CA 服务

### 2.2 CRL 列表

CRL 全称为 Certificate Revocation List，中文名称为证书吊销列表。CRL 里存储了被注销证书的序列号、注销时间和注销原因，同时，为保证 CRL 的有效性，CRL 携带了CA 的签名。

CRL 管理列举 CRL 列表，CRL 按照签名算法不同，分为 RSA CRL 和 SM2 CRL。通过CRL 管理可实现手动签发CRL、下载CRL、查看和查找功能。

数字证书认证系统用户手册

<html><body><table><tr><td>S 密码服务管理平台</td><td colspan="7"></td></tr><tr><td></td><td colspan="6">首页 CRLAE置X CRL列表X</td></tr><tr><td>数据加解密 签名验签</td><td colspan="2"></td></tr><tr><td>√</td><td colspan="2">CRL类型 增量&全量 √</td></tr><tr><td>密胡管理 日文件加密</td></tr><tr><td>生成 目数据库加密</td></tr><tr><td> 时间藏</td></tr><tr><td>CRL类型 协同签名 增量</td></tr><tr><td>动态令牌 全量</td></tr><tr><td>202311 增量</td></tr><tr><td>all 电子签章 202311</td></tr><tr><td>旱SSLVPN加密通道 增量 202311 数字证书认证 全量 al</td></tr><tr><td>20 SHA256WIthRSA 4 SMM3WitnSM2 20</td></tr><tr><td>CRLE置 全量 all CRL列表 证书中请</td><td>CN=sansec02,C=USA 2023-11-01 16:38:.51 2023-11-01 17:38:51 下载 SHA256WithRSA CN=sansec01,C=USA 2023-11-0116:38:51 2023-11-017:38:51</td></tr><tr><td>证书管理 证书下载 机构管理</td><td>下就</td></tr></table></body></html>

列表说明如下表所示：

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>CRL类型</td><td>CRL的类型，类型有完全CRL和增量CRL</td></tr><tr><td>增量ID</td><td>增量CRL的记录ID</td></tr><tr><td>证书数量</td><td>CRL中包含的证书个数</td></tr><tr><td>签名算法</td><td>CRL的签名算法</td></tr><tr><td>签发者</td><td>CA的信息，对应CA证书的使用者信息</td></tr><tr><td>生成时间</td><td>本次签发CRL的时间</td></tr><tr><td>下次生成时间</td><td>下次签发CRL的时间</td></tr><tr><td>发布时间</td><td>发布 CRL的时间</td></tr></table></body></html>

### 1. 签发 CRL

点击生成按钮，等待程序响应

数字证书认证系统用户手册

![](images/51dd6832505635b8f09e7cc925d1d386cf2864e97858462f037197c45c85b86a.jpg)
```text
这是一张显示密码服务管理平台界面的截图，具体展示的是CRL（证书撤销列表）配置中的CRL列表部分。左侧是功能菜单栏，包括数据加解密、签名验签、密钥管理、文件加密、数据库加密、时间戳、协同签名、动态令牌、电子印章、SSLVPN加密通道、数字证书认证等选项。当前选中的是“数字证书认证”下的“CRL配置”，进一步选中了“CRL列表”。

在右侧的主要内容区域，有一个表格列出了CRL的相关信息，包括CRL类型（增量或全量）、增量CRL id、证书数量、签名算法、签发者、生成时间、下次生成时间、发布时间和操作（下载）。表格中有六条记录，每条记录都包含了上述信息。

此外，在页面顶部有许可证有效期显示为2030年11月1日，以及当前操作员为“oper”。页面右上角还有大屏显示的图标和选项。
```


当前CA 可以实现完全CRL 和增量CRL，根据系统设置的CRL 生成策略，如果只选择完全CRL，每次生成的 CRL 都会覆盖前一同类型 CRL，因此列表里只有一个 RSA 算法CRL 和一个SM2 算法CRL；增量CRL 会生成每个月产生的证书注销列表，同一个月的会覆盖。

### 1. 下载 CRL。

<html><body><table><tr><td>CRL类型</td><td>增量CRL id</td><td>证书数量</td><td>签名算法</td><td>签发者</td><td>生成时问</td><td>下次生成时问</td><td>发布时问</td><td>操作</td></tr><tr><td>增量</td><td>202309</td><td></td><td>SHA256WITHRSA</td><td>CN=rsatest,C=CN</td><td>2023-09-26 16:42:46</td><td>2023-09-26 17:42:46</td><td>2023-09-26 16:42:46</td><td>下载</td></tr><tr><td>全量</td><td>al</td><td>0</td><td>SHA256WITHRSA</td><td>CN=rsatest,C=CN</td><td>2023-09-26 16:42:46</td><td>2023-09-26 17:42:46</td><td>2023-09-26 16:42:46</td><td>下载</td></tr></table></body></html>

单击选择一个CRL，点击下载按钮，等待程序响应。

### 2. 查找 CRL。

选择需要查找的CRL 类型，点击查询，查询得到期望的数据。

### 2.3 证书申请

证书申请分为三种：设备证书申请、用户证书申请。设备证书申请是指使用 P10 请求文件的形式进行证书的申请；用户证书申请是将用户信息录入进行证书的申请。

#### 2.3.1 设备证书申请

使用证书请求文件（PKCS10）向CA 提交证书申请请求。注册操作员具有设备证书申请权限。

1. 选择菜单 证书申请 > 设备证书申请，进入设备证书申请页面，如下图所示。

数字证书认证系统用户手册

设备证书申请

![](images/24a02fb66bb79899ab2ee861f84c3629d79983ddaae65a4af7dc9d579e6fa3f8.jpg)
```text
这是一张显示证书请求文件上传界面的图片。界面上有一个蓝色的“上传文件”按钮，提示用户只能上传证书文件。下方有一个下拉菜单，标记为“CA”，用户需要从中选择一个选项。此外，界面上还有两个选择项：“是否使用模板”和“是否为双证”，每个选择项都有“是”和“否”两个选项，当前都选择了“否”。最后，界面底部有两个按钮，一个是蓝色的“提交”按钮，另一个是灰色的“重置”按钮。
```


配置参数说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书请求文件</td><td>用于上传P10请求</td></tr><tr><td>CA</td><td>选择签发的CA</td></tr><tr><td>是否使用模版</td><td>申请的证书是否使用模版</td></tr><tr><td>是否为双证</td><td>申请的证书是否为双证</td></tr></table></body></html>

2. 选择证书模板，上传证书请求文件，填写证书有效期，注意证书模板中公钥算法要与证书请求文件中公钥的算法一致。  
3. 点击 提交 按钮，等待程序响应。

#### 2.3.2 用户证书申请

注册操作员具有用户证书申请权限。

1. 选择菜单 证书申请 > 用户证书申请，进入用户证书申请页面，如下图所示。

数字证书认证系统用户手册

![](images/5d26a1bd0bd73f17c7409aa744f1c01c129b14521159205c1b5a6f846c29cd4b.jpg)
```text
这张图片显示了一个用户证书申请的界面，包含了多个输入字段和选项。具体包括：

1. CA（证书颁发机构）：选择了“rsa_per”。
2. 密钥算法：选择了“RSA”。
3. 密钥长度：选择了“1024”。
4. 签名算法：选择了“SHA224WithRSA”。
5. 有效期：从2023年9月7日到2023年10月12日。
6. 模板：选择了“签发PFX证书模板”。
7. 是否使用自定义主题：选择了“否”。
8. 名称、国家、省份、城市、单位等信息的输入框，目前都未填写。
9. 另一个签名算法部分，同样选择了“SHA224WithRSA”，有效期相同，模板也选择了“签发PFX证书模板”，并且选择了使用自定义主题，但具体的名称、国家、省份、城市、单位、部门、邮箱等信息也未填写。
10. 软件证书口令的输入框，提示请输入密码。

这个界面主要用于用户在申请证书时填写相关信息，以生成符合要求的数字证书。
```


配置参数说明如下表所示。

提交 重置  

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>是否使用自定义主题</td><td>填写证书主题类型： 通用证书主题，填写对应项，系统自动拼接主题内容 自定义证书主题，填写指定格式的主题内容，如</td></tr><tr><td>国家</td><td>C=CN,CN=TEST 默认CN，即中国，不可修改</td></tr><tr><td>省份、城市、单位、 部门</td><td>一般填写使用者信息，存储在证书中使用者，取值要求： 只能包含汉字、字母、数字、空格、下划线、横杠和英文括号， 句首不能是下划线、横杠、英文右括号和空格，句尾不能是下划 线、横杠、英文左括号和空格 长度1-32个字符</td></tr><tr><td>名称</td><td>非必填项 用户名称，存储在证书中使用者的CN，取值要求：</td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td></td><td>只能包含汉字、字母、数字、空格、下划线、横杠和英文括号， 句首不能是下划线、横杠、英文右括号和空格，句尾不能是下划 线、横杠、英文左括号和空格 长度1-32个字符 必填项，不能为空</td></tr><tr><td>证书模板</td><td>签发证书时使用，包括系统模板和自定义模板。证书模板定义了 证书里证书类型、密钥算法/长度、签名算法和证书扩展等信息</td></tr><tr><td>有效期</td><td>选择起始日期</td></tr></table></body></html>

2. 填写用户信息，选择证书模板，填写证书有效期。  
3. 点击 提交 按钮，等待程序响应。  
4. 请求成功会出现是否立即下载证书

### 2.4 证书管理

#### 2.4.1 证书更新申请

证书更新操作就是注销原证书然后签发新的证书。只能对有效证书进行证书更新操作。证书更新操作包含证书更新申请。选择菜单 证书管理 > 证书更新申请，进入证书列表页面，如下图所示。

密码服务管理平台 国 许可证有效期：2030-11-01  
数据加解密  
签名验签  
密钥管理 密钥算法 查询  
文件加密  
目数据库加密 证书类型 序列号 签名算法 案钥算法 签发督 便用背 开始日期 结束日期 操  
时间藏  
协同签名  
國动态令牌  
电子签章  
SSLVPN加密通道  
数字证书认证CRLETCRL列表证书申请证书管理证书更新申请证书注销申请证书延期申请证书冻结申请证书解冻申请证书下载机构管理

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>签发者</td><td>证书签发者信息，即CA信息</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=xXX,O=xX,OU=xX,L=xX,L=xX,S=xX,C=CN，可 能含有其他信息，也可能缺少部分内容，依赖申请时输入的 信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr><tr><td>状态</td><td>证书的状态，状态类型有已申请冻结、已申请注销、已申请更 新和--。</td></tr></table></body></html>

#### 1. 更新证书。

点击选中要更新的证书，点击更新按钮，进入更新证书页面，如下图所示。

![](images/5e2eee506acca3822e9d7bb1b1d1aa02620a6237be5bc4651eb0d0822980decb.jpg)
```text
这是一张显示证书信息的图片，具体包括以下内容：

1. **证书类型**：签名证书。
2. **序列号**：2ce26abfcdaba5fcd650a222e758170b26a78f9cf198807ff045150fe2717d7a。
3. **签名算法**：SHA256WithRSA。
4. **签发者**：C=CN, CN=rsa_test。
5. **使用者**：C=CN, CN=rsa_test。
6. **有效期**：有一个输入框，用于选择开始时间和结束时间。

在图片的底部有两个按钮，分别是“确定”和“取消”。
```


输入需要更新的有效期

点击“确定”按钮，提交证书更新申请，点击“取消”按钮中断证书更新操作。根据实际情况填写证书信息，点击 提交 按钮，等待程序响应。

2. 查找证书。

数字证书认证系统用户手册

<html><body><table><tr><td>证书类型</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>请选择</td><td></td><td>序列号 请输入</td><td>状态 请选择</td><td></td><td>查询 重置</td></tr></table></body></html>

输入证书类型、序列号、状态点击查询按钮，得到期望的数据。

#### 2.4.2 证书注销申请

证书注销就是使证书永久失效。只能对有效证书进行证书注销操作。证书注销操作包含证书注销申请。

密码服务管理平台 三 许可证有效期：2030-11-01  
9 数据加解密  
签名验签  
A密钥管理 密钥算法 查询 重置  
文件加密  
目数据库加密 证书类型 序列号 签名算法 索钥算法 签发督 便用背 开始日期 结束日期 操作  
时间载  
协同签名  
國动态令牌  
电子签章  
SSLVPN加密通道  
数字证书认证CRL配TCRL列表证书申请证书管理证书更新申请证书注销申请证书延期申请证书冻结申请证书解冻申请证书下载机构管理

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双 证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>密钥算法</td><td>证书的密钥算法</td></tr><tr><td>签发者</td><td>证书签发者信息，即CA信息</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=xXX,O=xX,OU=xX,L=xX,L=xX,S=xX ,C=CN，可能含有其他信息，也可能缺少部 分内容，依赖申请时输入的信息。</td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr><tr><td>状态</td><td>证书的状态，状态类型有已申请冻结、已申请 注销、已申请更新和--。</td></tr></table></body></html>

#### 2. 注销证书。

<html><body><table><tr><td rowspan="2">证书类型</td><td colspan="2"></td><td rowspan="2">序列号</td><td colspan="2"></td><td rowspan="2">密钥算法 请选择</td><td colspan="2"></td><td rowspan="2">查询</td><td colspan="2">重置</td></tr><tr><td colspan="2">请选择</td><td colspan="2">请输入</td><td colspan="2"></td><td></td><td colspan="2"></td></tr><tr><td colspan="11"></td></tr><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td colspan="3">结束日期</td><td colspan="2">操作</td></tr><tr><td>签名证书</td><td>f0231980dd6d8f.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM...</td><td>C=CN,CN=test123</td><td>2023-11-0700:00:00</td><td colspan="2">2023-11-07 23:59:59</td><td colspan="2">注销</td></tr><tr><td>双证证书</td><td>ca80edd9a8564..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=</td><td>2023-11-06 14:15:50</td><td colspan="2">2023-11-19 14:15:50</td><td colspan="2">注销</td></tr><tr><td>签名证书</td><td>4eb119ce26a284..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,0U=.</td><td>2023-11-06 14:15:50</td><td colspan="2">2023-11-19 14:15:50</td><td colspan="2">注销</td></tr><tr><td>软件证书</td><td>3c210b2beb5de...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:14:48</td><td colspan="2">2024-11-05 14:14:48</td><td colspan="2">注销</td></tr><tr><td>签名证书</td><td>7e396cf769aa0b...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=...</td><td>2023-11-06 14:10:59</td><td colspan="2">2023-11-19 14:10:59</td><td colspan="2">注销</td></tr><tr><td>双证证书</td><td>1af196bc914a2b...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:10:59</td><td colspan="2">2023-11-19 14:10:59</td><td colspan="2">注销</td></tr><tr><td>双证证书</td><td>b5911c117a8679.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:09:35</td><td colspan="2">2023-11-19 14:09:35</td><td colspan="2">注销</td></tr><tr><td>签名证书</td><td>a205cfe0892c42...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,0U=..</td><td>2023-11-06 14:09:35</td><td colspan="2">2023-11-19 14:09:35</td><td colspan="2">注销</td></tr><tr><td>双证证书</td><td>bfc2bb172c7915.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:08:52</td><td colspan="2">2023-11-19 14:08:52</td><td colspan="2">注销</td></tr><tr><td>签名证书</td><td>516970cbaa925..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=. 2023-11-06 14:08:52</td><td colspan="2">2023-11-19 14:08:52</td><td colspan="3">注销</td></tr><tr><td>签名证书</td><td>d2d0f91b802c8e.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM.</td><td>C=CN,O=2,0U=. 2023-11-06 13:53:24</td><td colspan="2">2023-11-19 13:53:24</td><td colspan="3">注销</td></tr><tr><td>签名证书</td><td>e124r3ted31d51..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,CN=xX 2023-11-07 00:00:00</td><td colspan="2">2023-12-21 23:59:59</td><td colspan="3">注销</td></tr><tr><td>签名证书</td><td>f1a613a61fdbd0..</td><td>SM3WithsSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,CN=xx 2023-11-0700:00:00</td><td>2023-12-21 23:59:59</td><td></td><td></td><td></td><td colspan="2"></td></tr><tr><td colspan="8"></td></tr></table></body></html>

选择要注销的证书，点击注销按钮，等待程序响应。

3. 查找证书。

![](images/4fff50efc3f3e8840441b50ba27805829de99cbe7dc3d95890915e3041980a23.jpg)
```text
这张图片显示了一个用户界面，可能是一个证书查询或管理系统的部分。界面上有三个主要的输入字段和两个按钮。

1. **证书类型**：这是一个下拉菜单，用户可以选择不同的证书类型。当前显示为“请选择”，表示用户尚未选择任何选项。

2. **序列号**：这是一个文本输入框，用户可以在这里输入证书的序列号。当前显示为“请输入”，表示用户尚未输入任何内容。

3. **密钥算法**：这是一个下拉菜单，用户可以选择不同的密钥算法。当前显示为“请选择”，表示用户尚未选择任何选项。

在这些输入字段的右侧，有两个按钮：

- **查询**：这是一个蓝色的按钮，用户点击后可以提交查询请求。
- **重置**：这是一个灰色的按钮，用户点击后可以清除所有输入内容，恢复到初始状态。

这个界面设计简洁明了，方便用户进行证书的相关操作。
```


输入证书类型、序列号、状态点击查询按钮，得到期望的数据。

#### 2.4.3 证书冻结申请

证书冻结就是使证书暂时失效。只能对有效证书进行证书冻结操作。冻结后证书可通过解冻操作使证书恢复有效。证书冻结操作包含证书冻结申请。

1. 选择菜单 证书管理 > 证书冻结申请，进入证书列表页面，如下图所示

数字证书认证系统用户手册

列表说明如下表所示。  

<html><body><table><tr><td>证书关型</td><td colspan="2"></td><td></td><td colspan="2"></td><td colspan="2"></td><td colspan="2"></td><td>查询</td><td>重置</td></tr><tr><td></td><td colspan="2">请选择 √</td><td>序列号</td><td colspan="2">请输入</td><td colspan="2">密明算法 请选择</td><td colspan="2">√</td><td colspan="2"></td></tr><tr><td colspan="11"></td></tr><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td colspan="2">结束日期</td><td>操作</td><td colspan="2"></td></tr><tr><td>签名证书</td><td>f0231980dd6d8f.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM.</td><td>C=CN,CN=test123</td><td>2023-11-07 00:00:00</td><td colspan="2">2023-11-07 23:59:59</td><td>冻结</td><td colspan="2"></td></tr><tr><td>双证证书</td><td>ca80edd9a8564..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:15:50</td><td colspan="2">2023-11-19 14:15:50</td><td>冻结</td><td colspan="2"></td></tr><tr><td>签名证书</td><td>4eb119ce26a284..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:15:50</td><td colspan="2">2023-11-19 14:15:50</td><td colspan="2">冻结</td></tr><tr><td>软件证书</td><td>3c210b2beb5de..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=...</td><td>2023-11-06 14:14:48</td><td colspan="2">2024-11-05 14:14:48</td><td colspan="2">冻结</td></tr><tr><td>签名证书</td><td>7e396cf769aa0b..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:10:59</td><td colspan="2">2023-11-19 14:10:59</td><td colspan="2">冻结</td></tr><tr><td>双证证书</td><td>1af196bc9r4a2b...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:10:59</td><td colspan="2">2023-11-19 14:10:59</td><td colspan="2">冻结</td></tr><tr><td>双证证书</td><td>b5911c117a8679..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=...</td><td>2023-11-06 14:09:35</td><td colspan="2">2023-11-19 14:09:35</td><td colspan="2">冻结</td></tr><tr><td>签名证书</td><td>a205cfe0892c42..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA,CN=CA2</td><td>C=CN,O=2,OU=...</td><td>2023-11-06 14:09:35</td><td colspan="2">2023-11-19 14:09:35</td><td colspan="2">冻结</td></tr><tr><td>双证证书</td><td>bfc2bb172c7915..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:08:52</td><td colspan="2">2023-11-19 14:08:52</td><td colspan="2">冻结</td></tr><tr><td>签名证书</td><td>516970cbaa92...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=...</td><td>2023-11-06 14:08:52</td><td colspan="2">2023-11-19 14:08:52</td><td colspan="2">冻结</td></tr><tr><td>签名证书</td><td>d2d0f91b802c8e...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM..</td><td>C=CN,O=2,OU=... 2023-11-06 13:53:24</td><td colspan="2">2023-11-19 13:53:24</td><td colspan="2">冻绍</td><td colspan="2"></td></tr><tr><td>签名证书</td><td>e124f3fed31d51..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA,CN=CA2</td><td>C=CN,CN=xx</td><td>2023-11-0700:00:00</td><td>2023-12-21 23:59:59</td><td></td><td></td><td colspan="2"></td></tr><tr><td>签名证书</td><td>T1a613a61fdbdo.</td><td>SM3WitSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,CN=x00x</td><td>2023-11-0700:00:00</td><td>2023-12-21 23:59:59</td><td></td><td></td><td>冻结 冻结</td><td></td></tr></table></body></html>

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双 证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>密钥算法</td><td>证书的密钥算法</td></tr><tr><td>签发者</td><td>证书签发者信息，即CA信息</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=xxX,O=xX,OU=xX,L=xX,L=xX,S=xX, C=CN，可能含有其他信息，也可能缺少部分 内容，依赖申请时输入的信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr><tr><td>状态</td><td>证书的状态，状态类型有已申请冻结、已申请 注销、已申请更新和--。</td></tr></table></body></html>

2. 冻结证书。

数字证书认证系统用户手册

<html><body><table><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>操作</td></tr><tr><td>签名证书</td><td>10231980dd6dBr...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM...</td><td>C=CN,CN=test123</td><td>2023-11-0700:00:00</td><td>2023-11-07 23:59:59</td><td>冻结</td></tr><tr><td>双证证书</td><td>ca80ed9a8564.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:15:50</td><td>2023-11-19 14:15:50</td><td>冻结</td></tr><tr><td>签名证书</td><td>4eb119ce26a284.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2, OU=...</td><td>2023-11-06 14:15:50</td><td>2023-11-19 14:15:50</td><td>冻结</td></tr></table></body></html>

单击选中要冻结的证书，如选中的证书状态不是“有效”，则“冻结”按钮消失，表示不允许冻结操作。点击冻结按钮，提交冻结请求，等待程序响应。

#### 3. 3.查找证书。

![](images/59b29417366283b146464a2742211d62588591c2249cbff54783b547a34dc483.jpg)
```text
这张图片显示了一个用户界面，其中包含一个表单，用于选择和输入与证书相关的信息。表单中有三个主要部分：

1. **证书类型**：这是一个下拉菜单，当前显示为“请选择”，用户可以从中选择不同的证书类型。

2. **序列号**：这是一个文本输入框，旁边有提示文字“请输入”，用户需要在这里输入证书的序列号。

3. **密钥算法**：这也是一个下拉菜单，当前显示为“请选择”，用户可以从中选择不同的密钥算法。

在表单的右侧有两个按钮：“查询”和“重置”。用户可以点击“查询”按钮来提交表单并获取相关信息，或者点击“重置”按钮来清空所有输入字段，以便重新开始填写。

这个界面可能用于一个系统或应用程序中，用于管理和查询证书信息。
```


输入证书类型、序列号、状态点击查询按钮，得到期望的数据。

#### 2.4.4 证书解冻申请

证书解冻是证书冻结的逆操作，使冻结的证书恢复有效。证书解冻操作包含证书解冻申请和证书解冻审核。注册操作员进行解冻申请操作，审核操作员进行解冻审核操作。

1. 选择菜单 证书管理 > 证书解冻申请，进入证书列表页面，如下图所示。

<html><body><table><tr><td>证书类型</td><td colspan="3"></td><td>序列号</td><td colspan="2"></td><td colspan="2">密钥算法 请选择</td><td>查询 重置</td></tr><tr><td></td><td>请选择</td><td></td><td></td><td>请输入</td><td></td><td></td><td></td><td></td></tr><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>操作</td></tr><tr><td>双证证书</td><td>ca80ed9a85641.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=7..</td><td>2023-11-06 14:15:50</td><td>2023-11-19 14:15:50</td><td>解冻</td></tr><tr><td>签名证书</td><td>4eb119ce26a284..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td></td><td>2023-11-06 14:15:50</td><td></td><td></td></tr><tr><td>签名证书</td><td>10231980d6d815..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM2.</td><td>C=CN,O=2,OU=7. C=CN,CN=test123</td><td>2023-11-07 00:00:00</td><td>2023-11-19 14:15:50 2023-11-07 23:59:59</td><td>解冻 解冻</td></tr></table></body></html>

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双 证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>签发者</td><td>证书签发者信息，即CA信息</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=xxx,O=xX,OU=xX,L=xX,L=xx,S=xX, C=CN，可能含有其他信息，也可能缺少部分 内容，依赖申请时输入的信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td></td><td></td></tr><tr><td>状态</td><td>证书的状态，状态类型有已申请冻结、已申请 注销、已申请更新和--。</td></tr></table></body></html>

#### 2. 解冻证书。

单击选中要解冻的证书，如选中的证书状态不是“已冻结”，则“解冻”按钮消失，表示不允许解冻操作。点击解冻按钮，等待程序响应。

<html><body><table><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>操作</td></tr><tr><td>双证证书</td><td>ca80edd9a85641.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,0U=7.</td><td>2023-11-06 14:15:50</td><td>2023-11-19 14:15:50</td><td>解冻</td></tr><tr><td>签名证书</td><td>4eb119ce26a284..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,0=2,OU=7.</td><td>2023-11-06 14:15:50</td><td>2023-11-19 14:15:50</td><td>解冻</td></tr><tr><td>签名证书</td><td>f0231980dd6d8t5..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM2.</td><td>C=CN,CN=test123</td><td>2023-11-0700:00:00</td><td>2023-11-07 23:59:59</td><td>解冻</td></tr></table></body></html>

#### 3. 查找证书。

![](images/7ef69749ff1127ad14ba26a51cd63d1109aa47c9f3df6b9d35c99cdd29ecedbb.jpg)
```text
这张图片显示了一个用户界面，可能是一个证书查询或管理系统的部分。界面上有三个主要的输入字段和两个按钮。

1. **证书类型**：这是一个下拉菜单，当前显示为“请选择”，用户可以从中选择不同的证书类型。
2. **序列号**：这是一个文本输入框，旁边有提示文字“请输入”，用户需要在这里输入证书的序列号。
3. **密钥算法**：这也是一个下拉菜单，当前显示为“请选择”，用户可以从中选择不同的密钥算法。

在这些输入字段的右侧，有两个按钮：
- **查询**：蓝色按钮，用户点击后可以进行查询操作。
- **重置**：灰色按钮，用户点击后可以清空所有输入字段，恢复到初始状态。

整个界面设计简洁明了，方便用户进行操作。
```


输入证书类型、序列号、状态点击查询按钮，得到期望的数据。

#### 2.4.5 证书延期申请

证书延期操作是对已有的证书有效期后延，延长证书的使用期限。

1. 选择菜单 证书管理 > 证书延期申请，进入证书列表页面，如下图所示。

<html><body><table><tr><td rowspan="2">证书类型</td><td colspan="2"></td><td rowspan="2">序列号</td><td colspan="2"></td><td rowspan="2">密钥算法</td><td></td><td></td><td rowspan="2"></td></tr><tr><td colspan="2"></td><td colspan="2">请输入</td><td>请选择</td><td>查询 重置</td></tr><tr><td></td><td colspan="8"></td></tr><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>操作</td><td></td></tr><tr><td>软件证书</td><td>3c210b2beb5de..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:14:48</td><td>2024-11-05 14:14:48</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>7e396ct769aa0b.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=...</td><td>2023-11-06 14:10:59</td><td>2023-11-19 14:10:59</td><td>延期</td><td></td></tr><tr><td>双证证书</td><td>1af196bc914a2b..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 14:10:59</td><td>2023-11-19 14:10:59</td><td>延期</td><td></td></tr><tr><td>双证证书</td><td>b5911c117a8679..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:09:35</td><td>2023-11-19 14:09:35</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>a205cfe0892c42.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:09:35</td><td>2023-11-19 14:09:35</td><td>延期</td><td></td></tr><tr><td>双证证书</td><td>bfc2bb172c7915.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:08:52</td><td>2023-11-19 14:08:52</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>516970cbaa925..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:08:52</td><td>2023-11-19 14:08:52</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>d2d0m91b802c8e..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM.</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 13:53:24</td><td>2023-11-19 13:53:24</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>e124r3fed31d51..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,CN=xx</td><td>2023-11-07 00:00:00</td><td>2023-12-21 23:59:59</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>f1a613a61fdbdo.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,CN=xx</td><td>2023-11-07 00:00:00</td><td>2023-12-21 23:59:59</td><td>延期</td><td></td></tr><tr><td>双证证书</td><td>bfo10f7677108b...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM.</td><td>C=CN,O=2,OU=.</td><td>2023-11-06 09:28:26</td><td>2023-11-19 09:28:26</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>daab12de65d3e..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM.</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 09:28:26</td><td>2023-11-19 09:28:26</td><td>延期</td><td></td></tr><tr><td>签名证书</td><td>356830ec3aaa...</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=SM..</td><td>C=CN,O=2,0U=..</td><td>2023-11-06 09:27:32</td><td>2023-11-19 09:27:32</td><td>延期</td><td></td></tr></table></body></html>

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双证签 名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA 内唯一标识证书</td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td></td><td></td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>签发者</td><td>证书签发者信息，即CA信息</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=xxx,O=xx,OU=xX,L=xX,L=xx,S=xX,C= CN，可能含有其他信息，也可能缺少部分内容， 依赖申请时输入的信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr><tr><td>状态</td><td>证书的状态，状态类型有已申请冻结、已申请注 销、已申请更新和--。</td></tr></table></body></html>

#### 2. 证书延期

<html><body><table><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>操作</td></tr><tr><td>软件证书</td><td>3c210b2beb5de.</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:14:48</td><td>2024-11-05 14:14:48</td><td>延期</td></tr><tr><td>签名证书</td><td>7e396cf769aa0b..</td><td>SM3WithSM2</td><td>SM2</td><td>C=USA, CN=CA2</td><td>C=CN,O=2,OU=..</td><td>2023-11-06 14:10:59</td><td>2023-11-19 14:10:59</td><td>延期</td></tr></table></body></html>

选择要延期的证书，点击延期按钮，进入证书延期页面：

![](images/ca8b00ac0bb9c1d0d25d3bcdb77d1ac5deadf555b272da332b660d48772e4577.jpg)
```text
这张图片显示了一个证书延期的界面，具体内容如下：

- **证书类型**：签名证书
- **序列号**：b4773c2163f0163a0798640210cba2f9999bd14f37104fe0fdb295b5a642c8df
- **签名算法**：SM3WithSM2
- **签发者**：C=CN, CN=sm2_test
- **使用者**：C=CN, CN=sm2_test

在界面底部有一个标有星号的“有效期”输入框，提示用户需要填写证书的有效期。下方有两个按钮：“确定”和“取消”，分别用于确认延期操作或取消当前操作。

这个界面主要用于管理和更新数字证书的有效期，确保其在安全通信中的持续有效性。
```


填写有效期，单位天，点击 确定 按钮，等待程序响应。

数字证书认证系统用户手册

#### 3. 查找证书。

![](images/758a793635e3d7db1b436e385e55aeba00750b1093208d0bc96e2846cd478520.jpg)
```text
这张图片显示了一个用户界面，其中包含一个表单，用于输入与证书相关的详细信息。表单中有三个主要的输入字段：

1. **证书类型**：这是一个下拉菜单，用户可以选择不同的证书类型。当前显示为“请选择”，表示用户尚未选择任何选项。

2. **序列号**：这是一个文本输入框，用户可以在这里输入证书的序列号。当前显示为“请输入”，表示用户尚未输入任何内容。

3. **密钥算法**：这是一个下拉菜单，用户可以选择不同的密钥算法。当前显示为“请选择”，表示用户尚未选择任何选项。

在表单的右侧有两个按钮：

- **查询**：蓝色按钮，用户点击后可以提交表单进行查询。
- **重置**：灰色按钮，用户点击后可以清空所有输入字段，恢复到初始状态。

这个界面可能用于在一个系统中查询或管理证书信息。
```


输入证书类型、序列号、状态点击查询按钮，得到期望的数据。

### 2.5 证书下载

按照证书状态，证书下载分为有效证书下载、注销证书下载、过期证书下载。

#### 2.5.1 有效证书下载

1. 选择菜单 证书下载 > 有效证书下载，进入证书列表页面，如下图所示。

Q 密码服务管理平台 三 许可证有效期：2030-11-01  
9 数据加解密  
签名验签  
只 密钥管理 证书类型 序列号 密明算法 查询  
文件加密  
目数据库加密 证书类型 序列号 签名算法 宏钥算法 使用者 签发者 开始日期 结束日期 操作  
时间藏  
协同签名  
动态令牌  
电子签章  
早SSLVPN加密通道  
数字证书认证CRL配置CRL列表证书申请证书管理证书下载有效证书下载注销证书下载过期证书下载机构管理证书模板资源信息

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>密钥算法</td><td>证书的密钥类型</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=XXX,O=XX,OU=XX,L=XX,L=XX,S=xX,C=CN，可能含 有其他信息，也可能缺少部分内容，依赖申请时输入的信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr></table></body></html>

#### 2. 下载证书。

<html><body><table><tr><td>证书类型</td><td>序列号</td><td>签名算法</td><td>密钥算法</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>操作</td></tr><tr><td>签名证书</td><td>b4773c2163t016..</td><td>SM3WithSM2</td><td>SM2</td><td>C=CN,CN=sm2_test</td><td>2023-09-05 00:00:00</td><td>2023-10-25 23:59:59</td><td>下载</td></tr></table></body></html>

选择要下载的证书，点击下载按钮，等待程序响应。

#### 3. 查找证书。

输入密钥算法，点击查询，得到期望的数据。

#### 2.5.2 注销证书下载

1. 选择菜单 证书下载 > 注销证书下载，进入证书列表页面，如下图所示。

😴 密码服务管理平台 国 许可证有效期：2030-11-01  
数据加解密  
签名验签证书类型 序列号 密明算法 查询  
密钥管理  
文件加密  
目数据库加密 证书类型 序列号 签名算法 宏钥算法 使用者 签发者 开始日期 结束日期 操作  
时间藏  
日协同签名  
國动态令牌  
感电子签章  
SSLVPN加密通道  
数字证书认证CRL配E置CRL列表证书申请证书管理 SHA256WitRSA证书下载有效证书下载注销证书下载过期证书下载机构管理证书模板资源信息4

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>密钥算法</td><td>证书的密钥类型</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=xXX,O=xX,OU=xX,L=XX,L=XX,S=XX,C=CN，可能含 有其他信息，也可能缺少部分内容，依赖申请时输入的信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr><tr><td>注销日期</td><td>证书注销时间</td></tr></table></body></html>

2. 下载证书。

选择要下载的证书，点击下载按钮，等待程序响应。

3. 查找证书。

输入密钥算法，点击查询，得到期望的数据。

#### 2.5.3 过期证书下载

1. 选择菜单 证书下载 > 过期证书下载，进入证书列表页面，如下图所示。

![](images/7d0c07ea2248bb33fd028cc26d77751ff2e159e38b686fe3fc5e17f7470a39dd.jpg)
```text
这是一张显示密码服务管理平台界面的截图，具体展示的是“过期证书下载”页面。
```


列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>证书类型</td><td>证书的类型，类型有签名证书、软件证书、双证签名证书</td></tr><tr><td>序列号</td><td>证书序列号，在当前CA内唯一标识证书</td></tr><tr><td>签名算法</td><td>证书的签名算法</td></tr><tr><td>密钥算法</td><td>证书的密钥类型</td></tr><tr><td>使用者</td><td>证书使用者信息，格式为 CN=XXX,O=XX,OU=xX,L=XX,L=XX,S=xX,C=CN，可能含 有其他信息，也可能缺少部分内容，依赖申请时输入的信息。</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书失效时间</td></tr></table></body></html>

2. 下载证书。

选择要下载的证书，点击下载按钮，等待程序响应。

点击“保存”按钮，下载证书到本地。点击“取消”按钮，终止下载流程。

3. 3．查找证书。

输入密钥算法，点击查询，得到期望的数据。

## 2.6 机构管理

### 2.6.1 CA 证书管理

<html><body><table><tr><td>密码服务管理平台</td><td colspan="7">许可证有效期：2030-11-01 四大屏 操作员|oper </td></tr><tr><td>数据加解密</td><td colspan="6">首页有效证书下载×注销证书下载×过期证书下载×CA证书管理×</td></tr><tr><td>签名验签 密钥管理</td><td colspan="6">别名</td></tr><tr><td>文件加密 数据库加密</td><td colspan="6">请输入 密钥类型 请选择 √ 状态请选样 √ 查询 重置</td></tr><tr><td>时间藏</td><td colspan="6"></td></tr><tr><td>新增 ID</td><td colspan="6"></td></tr><tr><td>协同签名 171965100..</td><td>别名 签名算法 123456ab123. SHA256WithRSA</td><td>签发者 CN=123456ab123456ab12.</td><td>使用者 CN=123456ab123456ab12..</td><td>开始日期 2023-11-01 00:00:00</td><td>结束日期 状态 2023-11-08 23:59:59 可用</td><td>操作 更新下载下载(随)禁用</td></tr><tr><td>动态令牌 171965017..</td><td>SHA256WithRSA</td><td>CN=1,C=USA,ST=1,L=1,0.</td><td>CN=1,C=USA,ST=1,L=1,0.</td><td>2023-11-0100:00.00</td><td>2023-11-22 23:59:59 可用</td><td>更新 下载下载(链)禁用</td></tr><tr><td>电子签章 171964791..</td><td>SHA256WIhRSA</td><td>CN=1,C=CN,ST=1,L=1,O=.</td><td>CN=1,C=CN,ST=1,L=1,O=.</td><td>2023-11-0100:00:00 2023-11-16 23:59:59</td><td>可用</td><td>更新 下载下载(迪)禁用</td></tr><tr><td>早SSLVPN加密通道 171964782.</td><td>aasdasd SHA256WithRSA</td><td>CN=1,C=USA,ST=1,L=1,0.</td><td>CN=1,C=USA,ST=1,L=1,O.</td><td>2023-11-01 00:00:00</td><td>2023-12-26 23:59:59 可用</td><td></td></tr><tr><td>数字证书认证 171964669..</td><td>SHA256WihRSA</td><td>CN=123456781234567812..</td><td></td><td>2023-11-01 00:00:00</td><td>可用</td><td>更新下载下载(链)禁用</td></tr><tr><td>CRUET</td><td>12345678123. 1asdasdasd SHA256WIhRSA</td><td>CN=1,C=USA</td><td>CN=123456781234567812..</td><td>2023-11-30 23:59:59 2023-11-01 00:00:00 2023-11-30 23:59:59</td><td>可用</td><td>更新下载下载(链)禁用</td></tr><tr><td>171964653.. CRL列表</td><td>SHA256WIhRSA</td><td>CN=123,C=USA</td><td>CN=1,C=USA CN=123,C=USA</td><td>2023-11-01 00:00:00 2023-11-03 23:59:59</td><td></td><td>更新下载下载(随)禁用</td></tr><tr><td>171964138.. 123</td><td>SHA256WiIhRSA</td><td>CN=过期验证.C=USA</td><td>CN=过期验证.C=USA</td><td>2023-11-0100:00:00</td><td>可用</td><td>更新下载下载(随)禁用</td></tr><tr><td>171963280.. 过明验证</td><td>SM3WithSM2</td><td>CN=sansec02,C=USA</td><td>CN=sansec02,C=USA</td><td>2023-11-0123:59:59</td><td>禁用</td><td>下载下载(链)启用</td></tr><tr><td>171962532. sansec02 171962526.. sansec01</td><td>SHA256WithRSA</td><td>CN=sansec01,C=USA</td><td>CN=sansec01,C=USA</td><td>2023-11-01 00:00:00 2027-12-30 23:59:59</td><td>可用</td><td>更新 下载下载(题)禁用</td></tr><tr><td>证书下载 机构管理</td><td></td><td></td><td>2023-11-01 00:00:00</td><td>2028-12-2823:59:59</td><td>可用</td><td>更新下载下载(链)禁用</td></tr><tr><td>CA证书管理 RA管理</td><td colspan="6"></td></tr><tr><td>证书模板</td><td colspan="6"></td></tr><tr><td>资源信息 SIM盾管理</td><td colspan="6"></td></tr></table></body></html>

列表说明如下表所示：

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>ID</td><td>证书在数据库存储ID</td></tr><tr><td>別名</td><td>证书名称</td></tr><tr><td>签名算法</td><td>证书签名算法</td></tr><tr><td>签发者</td><td>证书签发者信息</td></tr><tr><td>使用者</td><td>证书使用者信息</td></tr><tr><td>开始日期</td><td>证书生效时间</td></tr><tr><td>结束日期</td><td>证书过期时间</td></tr><tr><td>证书状态</td><td>显示证书状态</td></tr><tr><td>操作</td><td>对证书的操作</td></tr></table></body></html>

提供CA 证书管理的相关功能，包括新增、编辑、查询、更新、下载、下载（链），禁用等功能。

#### 2.6.1.1 搜索栏

![](images/8bc79069309f18a870b170faccfc43df7831047e018832bc4b2a0f214256ea3f.jpg)
```text
这张图片显示了一个用户界面，可能是一个管理或配置系统的部分。界面上有三个主要的输入区域和两个按钮。

1. **别名**：这是一个文本输入框，提示用户“请输入”，意味着用户可以在这里输入一个名称或标签。
2. **密钥类型**：这是一个下拉菜单，当前显示为“请选择”，表示用户需要从列表中选择一个密钥类型。
3. **状态**：这也是一个下拉菜单，同样显示为“请选择”，意味着用户需要选择一个状态选项。

在这些输入区域的右侧，有两个按钮：
- **查询**：这个蓝色按钮可能是用来提交用户的输入，进行搜索或查询操作。
- **重置**：这个灰色按钮可能是用来清除所有输入，将界面恢复到初始状态。

整体来看，这个界面设计简洁明了，旨在帮助用户通过选择和输入来执行特定的查询或配置任务。
```


搜索栏可以根据别名、密钥类型和装填查询 CA 证书，输入搜索条件，点击查询，符合条件的CA 证书会显示在下方的列表中。

#### 2.6.1.2 新增证书

自签CA

数字证书认证系统用户手册

<html><body><table><tr><td>←返回</td><td>新增CA证书</td></tr><tr><td></td><td></td></tr><tr><td>自签CA 导入证书</td><td></td></tr><tr><td>*别名</td><td></td></tr><tr><td></td><td>密钥算法 请选择</td></tr><tr><td>*密钥索引 请选择</td><td></td></tr><tr><td>签名算法</td><td></td></tr><tr><td></td><td>请选择</td></tr><tr><td></td><td>*有效期 开始时间</td></tr><tr><td></td><td></td></tr><tr><td></td><td>*证书模板： 请选择</td></tr><tr><td></td><td>重置</td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr></table></body></html>

点击“新增按钮”，输入相关信息。

配置参数说明如下表所示：

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>别名</td><td>证书的名称</td></tr><tr><td>密钥算法</td><td>选择证书的密钥算法类型</td></tr><tr><td>密钥索引</td><td>公钥在密码卡中的容器索引</td></tr><tr><td>签名算法</td><td>根证书支持的签名算法： 密钥类型为RSA时，支持 SHA256WithRSA、SHA224WithRSA、SHA384WithRSA、 SHA512WithRSA 密钥类型为SM2 时，支持SM3WithSM2</td></tr><tr><td>有效期</td><td>选择开始时间和结束时间</td></tr><tr><td>证书模版</td><td>选择证书模版</td></tr><tr><td>是否使用自定义主题</td><td>选择是否使用通用证书主题： 是：填写对应项，系统自动拼接主题内容 否：填写指定格式的主题内容，如C=CN,CN=TEST</td></tr><tr><td>名称</td><td>更新的CA根证书名称，存储在证书中使用者的CN</td></tr><tr><td>国家</td><td>CN 和 USA</td></tr><tr><td>省份、城市、单位、 部门</td><td>一般填写使用者信息，存储在证书中使用者，取值要求： 只能包含汉字、字母、数字、空格、下划线、横杠和英文括号， 句首不能是下划线、横杠、英文右括号和空格，句尾不能是下划 线、横杠、英文左括号和空格 长度1-32个字符 非必填项</td></tr></table></body></html>

填写信息后，点击提交按钮，等待程序响应。

#### 导入证书

数字证书认证系统用户手册

![](images/99176b4e3149083f6f1b011d1c5104bf8e739cb0b0f5a1a482004a5bf1a0edc8.jpg)
```text
这张图片显示的是一个网页界面，用于新增CA证书。界面上有“自签CA”和“导入证书”两个选项卡，当前选中的是“导入证书”。在“导入证书”选项卡下，有一个选择框询问是否导入私钥，当前选择的是“否”。下方有一个蓝色按钮，提示用户点击上传CA证书，说明只能上传cer文件，且不超过10kb。再往下是一个输入框，要求用户输入别名。页面底部有三个按钮，分别是“提交”、“重置”和“生成CSR”，其中“生成CSR”按钮旁边有一个问号图标，可能提供帮助信息。
```


#### 1、 选择是否导入私钥；

导入私钥: 是 否

\*CA私钥: 点击上传

只能上传pem编码的PKCS8私钥，且不超过10kb

是：按照要求导入私钥文件，导入CA 证书文件和输入别名；  
否：直接进入下一步。

#### 生成CSR

#### 2、 点击

按钮，

![](images/99c55539e65c763a4dc2232924a20687bd00769fc9e489649fc2e5bb6d7fb7b8.jpg)
```text
这张图片显示了一个生成证书请求的界面。界面上有五个必填字段，分别标记为星号（*），表示这些字段是必须填写的。这些字段包括：

1. 别名：这是一个文本输入框，用户需要在这里输入一个别名。
2. 密钥算法：这是一个下拉菜单，用户需要从选项中选择一个密钥算法。
3. 密钥索引：这也是一个下拉菜单，用户需要选择一个密钥索引。
4. 签名算法：同样是一个下拉菜单，用户需要选择一个签名算法。
5. 证书模板：最后一个下拉菜单，用户需要选择一个证书模板。

在这些字段下方，有两个按钮：“提交”和“重置”。用户可以点击“提交”按钮来提交他们的证书请求，或者点击“重置”按钮来清除所有输入并重新开始。
```


填写上面对应信息，点击提交，自动下载证书请求文件。

3、 用下载得到的证书请求文件在设备证书申请（操作详情见 2.3.1）中进行设备证书的申请；

#### 点击上传

4、 点击

按钮上传证书文件。

5、 别名和证书请求文件中的别名保持一致；  
6、 点击提交按钮，等待程序响应。

#### 2.6.1.3 更新证书

![](images/903b9bd85ba1e89eae4e788c66d014f9ae47a9fc2a7bf69278adb6ddb2138b1d.jpg)
```text
这张图片显示了一个数字证书申请表单的界面。表单中包含多个字段，用于填写与数字证书相关的详细信息。具体字段包括：

- 别名：已填写为“sm2_per”
- 密钥算法：已选择“SM2”
- 密钥索引：已选择“1号签名密钥”
- 签名算法：已选择“SM3WithSM2”
- 有效期：从2023年9月1日至2024年10月1日
- 证书模板：已选择“签发CA证书模板”
- 是否使用自定义主题：已选择“否”

此外，还有几个未填写的字段，包括名称、国家、省份、城市、单位和部门等，这些字段需要用户根据实际情况进行填写。

这个表单主要用于申请数字证书，确保在网络安全通信中能够进行身份验证和数据加密。
```


配置参数说明如下表所示：  

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>是否使用自定义主题</td><td>选择是否使用通用证书主题： 是：填写对应项，系统自动拼接主题内容 否：填写指定格式的主题内容，如C=CN,CN=TEST</td></tr><tr><td>名称</td><td>更新的CA根证书名称，存储在证书中使用者的CN，取值要 求： 只能包含汉字、字母、数字、空格、下划线、横杠和英文括号， 句首不能是下划线、横杠、英文右括号和空格，句尾不能是下划 线、横杠、英文左括号和空格</td></tr><tr><td>国家</td><td>长度1-32个字符 必填项，不能为空 默认CN，即中国</td></tr><tr><td>省份、城市、单位、 部门</td><td>一般填写使用者信息，存储在证书中使用者，取值要求： 只能包含汉字、字母、数字、空格、下划线、横杠和英文括号，</td></tr></table></body></html>

<html><body><table><tr><td></td><td>线、横杠、英文左括号和空格 长度1-32个字符 非必填项</td></tr><tr><td>密钥算法</td><td>默认要更新的CA根证书密钥算法，不可修改</td></tr><tr><td>签名算法</td><td>根证书支持的签名算法： 密钥类型为RSA时，支持 SHA256WithRSA、SHA224WithRSA、SHA384WithRSA、 SHA512WithRSA</td></tr><tr><td>密钥索引</td><td>密钥类型为SM2 时，支持SM3WithSM2 生成根证书的公钥在密码卡中的容器索引。</td></tr><tr><td>有效期</td><td>选择开始时间和结束时间。</td></tr><tr><td>证书模版</td><td>选择证书模版。</td></tr></table></body></html>

填写信息后，点击提交按钮，等待程序响应，如成功则弹出“更新证书成功”提示，否则弹出对应的错误提示。

#### 2.6.1.4 下载证书

#### 下载

<html><body><table><tr><td>ID</td><td>证书名称</td><td>签名算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>状态</td><td>操作</td></tr><tr><td>3</td><td>sm2_per</td><td>SM3WithSM2</td><td>CN=sm2_per,C=sm2_per</td><td>CN=sm2_per,C=sm2_per</td><td>2023-09-01 00:00:00</td><td>2024-10-01 23:59:59</td><td>可用</td><td>更新下载下载(链)禁用</td></tr></table></body></html>

#### 选择需要下载的证书点击下载按钮

#### 下载（链）

<html><body><table><tr><td>ID</td><td>证书名称</td><td>签名算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>状态</td><td colspan="2">操作</td></tr><tr><td>3</td><td>sm2_per</td><td>SM3WithSM2</td><td>CN=sm2_per,C=sm2_per</td><td>CN=sm2_per,C=sm2_per</td><td>2023-09-01 00:00:00</td><td>2024-10-01 23:59:59</td><td>可用</td><td>更新下载下载(链)禁用</td><td></td></tr><tr><td>2</td><td>rsa_per</td><td>SHA256WithRSA</td><td>CN=rsa_test,C=CN</td><td>CN=rsa_test,C=CN</td><td>2023-09-01 00:00:00</td><td>2023-11-01 23:59:59</td><td>可用</td><td>更新</td><td></td></tr><tr><td></td><td>test</td><td></td><td></td><td>CN=test,C=CN</td><td></td><td></td><td>创建中</td><td></td><td>PEM格式</td></tr></table></body></html>

选择要下载的证书链，点击下载（链）按钮，出现下载页面

#### 2.6.1.5 查看证书

数字证书认证系统用户手册

![](images/b756ede9c258c6b6ecc582110be5817c7a72a55f807144fa0dd17b7ec27526e3.jpg)
```text
这张图片显示了一个证书管理界面，具体展示了一张名为“sm2_per”的证书的详细信息。该证书使用SM3WithSM2签名算法，版本为3，序列号为r7e8b8d318af68506d758a4abd655b9d329c92a1873a3e8dc7fc90b4856c3699。证书的颁发者和主题均为CN=sm2_per,C=sm2_per，有效期从2023年9月1日到2024年10月1日。公钥类型为External SM2 Public Key，位数为0。此外，界面还显示了其他证书的信息，如ID、证书名称、签名算法、状态和操作选项等。
```


点击要查看的证书的证书名称，进入证书详细信息的界面。

#### 2.6.1.6 禁用证书

<html><body><table><tr><td>ID</td><td>证书名称</td><td>签名算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td><td>状态</td><td>操作</td></tr><tr><td>3</td><td>sm2_per</td><td>SM3WithSM2</td><td>CN=sm2_per,C=sm2_per</td><td>CN=sm2_per,C=sm2_per</td><td>2023-09-01 00:00:00</td><td>2024-10-01 23:59:59</td><td>可用</td><td>更新下载下载(链)禁用</td></tr></table></body></html>

点击禁用证书，证书状态从可用变为禁用状态。

### 2.6.2 RA 管理

#### 2.6.2.1 搜索栏

<html><body><table><tr><td>颁发者信息 请输入</td><td></td><td>查询</td><td>重置</td></tr></table></body></html>

搜索栏可以签颁发者信息查询 RA，输入搜索条件，点击查询，符合条件的 RA 会显示在下方的列表中。

![](images/24031e76c578c48767c3e40988fa0e009b43abb26b3f892dfcc5dd82bf3f28b9.jpg)
```text
这是一张显示密码服务管理平台界面的截图。界面左侧是一个垂直导航栏，列出了多个功能模块，包括数据加解密、签名验签、密钥管理、文件加密、数据库加密、时间戳、协同签名、动态令牌、电子签章、SSLVPN加密通道、数字证书认证、CRL配置、CRL列表、证书申请、证书管理、证书下载、机构管理、CA证书管理、RA管理、证书模板和资源信息等。

右侧是RA管理的具体内容页面，顶部有多个标签页，分别是首页、有效证书下载、注销证书下载、过期证书下载、CA证书管理和RA管理。当前选中的标签页是RA管理。

在RA管理页面中，有一个表格，列出了签名算法、颁发者信息、有效开始时间、有效结束时间、撤销时间、授权码、证书状态和操作等字段。表格下方显示“暂无数据”，表示当前没有相关的RA管理记录。

页面右上角显示了许可证有效期为2030年11月1日，并且有大屏和操作员信息的链接。
```


#### 2.6.2.2 RA 列表

数字证书认证系统用户手册

新增  

<html><body><table><tr><td>签名算法</td><td>颁发者信息</td><td>有效开始时问</td><td>有效结束时问</td><td>撤销时问</td><td>授权码</td><td>证书状态</td><td>操作</td></tr><tr><td>SM3WithsM2</td><td>C=sm2_per,CN=sm2_per</td><td>2023-09-18 14:01:29</td><td>2023-11-0100:00.00</td><td></td><td></td><td>可用</td><td>更新删除注销下就证书</td></tr><tr><td>SM3WithSM2</td><td>C=sm2_per,CN=sm2_per</td><td>2023-09-18 09:30:35</td><td>2023-11-01 00:00:00</td><td></td><td></td><td>可用</td><td>更新删除注销下载证书</td></tr></table></body></html>

列表说明如下表所示：

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>签名算法</td><td>RA证书签名算法</td></tr><tr><td>颁发者信息</td><td>CA授权给RA的算法，此 RA只能用此算法注册证书</td></tr><tr><td>有效开始时间</td><td>RA 有效开始时间</td></tr><tr><td>有效结束时间</td><td>RA有效结束时间</td></tr><tr><td>撤销时间</td><td>RA注销的时间</td></tr><tr><td>授权码</td><td>RA在线申请时，用授权码向CA认证。</td></tr><tr><td>证书状态</td><td>RA状态</td></tr></table></body></html>

#### 更新

选择要更新的RA，点击更新按钮，进入更新RA 页面：

![](images/29b36b92df1a09785ec9d513987e9e41a03ce3cd933ae3ca8a75b107fd2cdb2c.jpg)
```text
这张图片显示了一个证书更新的界面，包含以下内容：

1. 更新证书：选择“是”。
2. 申请方式：选择了“p10申请”。
3. 证书请求：有一个蓝色的“上传”按钮，提示.csr文件需要小于5MB。
4. 签发CA：选择了“sm2_per”。
5. 有效期（天）：从2023年9月18日到2023年11月1日。
```


授权CA编辑  

<html><body><table><tr><td>密钥类型</td><td>签名算法</td><td>签发者</td><td>使用者</td><td>开始日期</td><td>结束日期</td></tr><tr><td>RSA</td><td>SHA256WithRS A</td><td>CN=rsa_test,C=CN</td><td>CN=rsa_test,C=.</td><td>2023-09-01 00:0.</td><td>2023-11-01 23:5..</td></tr><tr><td>SM2</td><td>SM3WithSM2</td><td>CN=sm2_per,C=...</td><td>CN=sm2_per,C..</td><td>2023-09-01 00:0...</td><td>2024-10-01 23:5.</td></tr></table></body></html>

授权模版编辑  

<html><body><table><tr><td>名称</td><td>证书类型</td><td>创建时问</td><td></td><td>修改时问</td><td>描述</td></tr><tr><td>签发PFX证书..</td><td></td><td>2023-08-29 15:03:45</td><td></td><td></td><td></td></tr><tr><td>签发签名证书...</td><td></td><td>2023-08-29 15:03:54</td><td></td><td></td><td></td></tr><tr><td>签发双证描板</td><td></td><td>2023-0R-29 15:04-07</td><td></td><td></td><td></td></tr><tr><td>4</td><td></td><td>提交</td><td>重置</td><td></td><td></td></tr></table></body></html>

配置参数说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>RA信息</td><td>显示要更新的 RA信息</td></tr><tr><td>是否更新证书</td><td>选择是否更新 RA 证书</td></tr><tr><td>申请方式</td><td>RA申请的方式： P10申请 在线申请</td></tr><tr><td>RA证书请求</td><td>当选择P10申请时，在此上传P10请求</td></tr><tr><td>签发CA</td><td>签发CA</td></tr><tr><td>有效期</td><td>RA证书的有效期</td></tr><tr><td>授权算法</td><td>授权给 RA 的算法</td></tr><tr><td>授权模板</td><td>RA可用的证书模板</td></tr></table></body></html>

按照实际需求填写信息，点击提交按钮，等待程序响应。

#### 删除

<html><body><table><tr><td>签名算法</td><td>顾发者信息</td><td>有效开始时间</td><td>有效结束时间</td><td>撒销时间</td><td>授权码</td><td>证书状态</td><td>操作</td></tr><tr><td>SM3WIithSM2</td><td>C=sm2_per,CN=sm2_per</td><td>2023-09-18 14:01:29</td><td>2023-11-01 00:00:00</td><td></td><td></td><td>可用</td><td>更新删除注销下载证书</td></tr></table></body></html>

选择需要删除的RA，点击删除按钮。

#### 注销

<html><body><table><tr><td>签名算法</td><td>颁发者信息</td><td>有效开始时问</td><td>有效结束时问</td><td>撤销时间</td><td>授权码</td><td>证书状态</td><td>操作</td></tr><tr><td>SM3WithSM2</td><td>C=sm2_per,CN=sm2_per</td><td>2023-09-18 14:01:29</td><td>2023-11-01 00:00:00</td><td></td><td></td><td>可用</td><td>更新删除注销下载证书</td></tr></table></body></html>

选择需要注销的RA，点击注销按钮。

#### 下载证书

<html><body><table><tr><td>签名算法</td><td>颁发者信息</td><td>有效开始时问</td><td>有效结束时间</td><td>撒销时间</td><td>授权码</td><td>证书状态</td><td>操作</td></tr><tr><td>SM3WIthSM2</td><td>C=sm2_per,CN=sm2_per</td><td>2023-09-18 14:01:29</td><td>2023-11-01 00:00:00</td><td></td><td></td><td>可用</td><td>更新删除注销下载证书</td></tr></table></body></html>

选择需要下载的RA，点击下载按钮。

#### 2.6.2.3 RA 新增

点击新增按钮，进入RA 新增界面。

![](images/07e452bce4ded64c9dc381b537a34e70fdb1ccbdb447dcf4177577df6fae33ca.jpg)
```text
这是一张显示证书申请界面的截图，界面上有多个输入框和选项。
```


配置参数说明如下表所示：  

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>申请方式</td><td>RA申请的方式： P10申请</td></tr><tr><td>RA证书请求</td><td>在线申请 当选择P10申请时，在此上传P10请求</td></tr><tr><td>签发CA</td><td>签发CA</td></tr><tr><td>有效期</td><td>RA证书的有效期</td></tr><tr><td>授权算法</td><td>授权给 RA的算法</td></tr><tr><td>授权模板</td><td>RA可用的证书模板</td></tr></table></body></html>

1. 根据实际填写相关信息。

2. 如果选择P10 申请，需额外上传P10 请求文件。点击上传选择P10 文件，然后根据实际填写、选择相关信息。

3. 点击 提交 按钮，等待程序响应，如正确执行，则弹出提示“增加 RA 成功”，否则，弹出对应的错误提示。4. 添加RA 完成后，系统会跳转到RA 列表页面。

### 2.7 证书模板管理

#### 2.7.1 证书模板添加

业务管理员登录系统，添加证书模板，申请证书时，选择证书模板，CA 在签发证书时，将相应的信息写入证书的扩展属性里。

1. 通过选择菜单 证书模板管理 > 证书模板列表 > 新增,进入添加证书模板页面。

![](images/de3e2bec0541142fce22c610a65998303350282957fd3057095661caea4bf17b.jpg)
```text
这是一张显示“新增模板”界面的截图，界面上有多个输入框和选项。
```


<html><body><table><tr><td>基本信息</td><td>主题项 标准扩展</td><td>自定义扩展</td><td colspan="7"></td></tr><tr><td></td><td colspan="3">主题项名称</td><td colspan="2">是香必填</td><td></td><td colspan="3"></td></tr><tr><td></td><td></td><td>名称</td><td></td><td>●是</td><td>否</td><td></td><td>数量 1</td><td>+</td><td></td></tr><tr><td></td><td>国家</td><td></td><td></td><td>●是</td><td>香</td><td></td><td>1</td><td>+</td><td></td></tr><tr><td></td><td></td><td>省份</td><td></td><td>是</td><td>●否</td><td></td><td>1</td><td></td><td></td></tr><tr><td></td><td></td><td>城市</td><td></td><td>是</td><td>●否</td><td></td><td>1</td><td>+</td><td></td></tr><tr><td></td><td></td><td>单位</td><td></td><td>是</td><td>●否</td><td></td><td>1</td><td>+</td><td></td></tr><tr><td></td><td></td><td>部门</td><td></td><td>是</td><td>●否</td><td></td><td>1</td><td>+</td><td></td></tr><tr><td></td><td></td><td>邮箱</td><td></td><td>是</td><td></td><td></td><td></td><td>+</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>●否</td><td></td><td>1</td><td>+</td><td></td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td></td></tr><tr><td>基本信息 主题项 标准扩展 自定义扩展 - 名称</td></tr><tr><td>是香关键 扩展值 签发证书时由CA填写</td></tr><tr><td>颁发者密钥标识扩展 是 ●否 □ 主题密钥标识符扩展 是 ●否</td></tr><tr><td>签发证书时由CA填写 CRL分发点扩展策略 是 ●否 签发证书时由CA填写</td></tr><tr><td>□ 证书策略扩展策略 是 ●否 签发证书时填写</td></tr><tr><td></td></tr><tr><td>□ CA信息访问策略 是 ●否 签发证书时填写</td></tr><tr><td>□ 基本约束扩展 是 ●否 是否为CA证书 证书路径长度： -1</td></tr><tr><td>策略限制扩展 是 ●否 取值方式： 必须指定 非必须指定</td></tr><tr><td>□ 策略映射扩展 是 ●否 取值方式： ●必须指定 非必须指定</td></tr><tr><td>□ 企业工商注册号扩展 是 ●否 取值方式： ●必须指定 非必须指定</td></tr><tr><td>□ 个人社会保险号扩展 是 ●否 取值方式： 必须指定 非必须措定</td></tr><tr><td>□ 企业组织机构代码扩展 C 是 取值方式： 必须指定 非必须指定</td></tr><tr><td></td></tr><tr><td>企业税号扩展 是 ●否 取值方式： ● 必须指定 非必须指定 MS证书模板扩展 是 ●否 其他信息： 。 城控制器 智能卡登录用户</td></tr><tr><td>数字签名 不可否认 密钥加密 数据加密 密钥协商 − 证书签名</td></tr><tr><td>□ 密钥用法扩展 ●是 ●否 CRL签名 只用作加密 只用作解密 邮件保护 时间戳</td></tr><tr><td>服务端认证 客户端认证 □ 服务端认证 − OCSPSigning 增强密钥用法扩展 是 ●否 智能卡登录用户 □ 任意密明用途 □ IpSec终端系统 IpSec隧道 IpSec用户</td></tr><tr><td>SSL.client SSLserver SMIME Objectsigning SSLCA SMIMECA Netscape证书类型扩展 是 ●否 ObjectsigningcCA</td></tr><tr><td>其他名称 取值方式： 必须指定 0 非必须指定</td></tr><tr><td>北必活址</td></tr></table></body></html>

←返回 新增模板

<html><body><table><tr><td>基本信息</td><td>主题项</td><td>标准扩展 自定义扩展</td><td></td><td></td><td></td><td></td></tr><tr><td>-</td><td>名称</td><td></td><td>是否必填</td><td>扩展oid</td><td>是否关键</td><td>扩展值</td></tr><tr><td>□</td><td>all</td><td></td><td>否</td><td>*******</td><td>0</td><td>12341234</td></tr></table></body></html>

配置参数说明如下表所示。  

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>名称</td><td>模板的名称，取值要求有： 不能为空 长度要求1-32字符 只能包含汉字、字母、数字、空格、下划线、横杠和英文括号， 句首不能是下划线、横杠、英文右括号和空格，句尾不能是下划 线、横杠、英文左括号和空格</td></tr><tr><td>证书类型</td><td>名称可重复 此模板签发的证书类型，包含： 签名证书 双证书</td></tr><tr><td>是否发布 LDAP</td><td>PFX 选择是否发布 LDAP</td></tr><tr><td>是否发布OCSP</td><td>选择是否发布 OCSP</td></tr><tr><td>描述</td><td>添加描述</td></tr><tr><td>主题名称</td><td>根据需求设置主题项是否必填、数量</td></tr><tr><td>标准证书扩展 自定义扩展</td><td>根据需求进行选择各项扩展是否关键以及扩展值 自定义扩展即是3.10.2添加的证书扩展。</td></tr></table></body></html>

2. 根据实际填写和选择模板信息；3. 点击提交按钮，等待程序响应

#### 2.7.2 自定义扩展管理

证书模板管理列举已添加的证书模板列表。通过本列表可以对证书模板管理，包括添加、修改、删除、查看和查找。

1. 选择菜单 自定义扩展管理，进入证书模板列表页面，如下图所示。

![](images/bd519455ce539240e15644c2a566b1a7559ac1200534d0080b67ef98138062eb.jpg)
```text
这张图片显示了一个证书管理界面，列出了多个证书的详细信息。表格中有五个主要列：名称、证书类型、描述、创建时间和操作。具体来说：

1. 名称为“all”的证书，类型为双证书，创建时间为2023-09-21 18:10:52。
2. 名称为“签发双证模板”的证书，类型为双证书，创建时间为2023-08-29 15:04:07。
3. 名称为“签发签名证书模板”的证书，类型为签名证书，创建时间为2023-08-29 15:03:54。
4. 名称为“签发PFX证书模板”的证书，类型为pfx，创建时间为2023-08-29 15:03:45。
5. 名称为“签发CA证书模板”的证书，类型为签名证书，创建时间为2023-08-29 14:50:08。

在操作列中，每个证书都有“编辑”和“删除”两个选项，允许用户对证书进行相应的管理操作。此外，界面顶部有一个搜索框和一个重置按钮，用户可以通过输入名称或选择证书类型来查询特定的证书。左上角还有一个“新增”按钮，可能用于添加新的证书。
```


列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>名称</td><td>模板的名称</td></tr><tr><td>证书类型</td><td>对应的证书类型</td></tr><tr><td>描述</td><td>对模板的描述</td></tr><tr><td>创建时间</td><td>证书模板创建时间</td></tr></table></body></html>

2. 编辑编辑证书模板。

选择要修改的证书模板项，点击编辑按钮，跳转到证书模板编辑页面。根据实际情况修改证书模板信息，输入项与添加证书模板要求一样。如下图所示：

←返回 编辑模板

提交

![](images/71e0131612d1ef033f4cbaee57479e063ce2a15ac0e122f3b0a9801b56ed478c.jpg)
```text
这是一张显示证书配置界面的图片，具体包括以下内容：

1. **基本信息**：这是当前选中的标签页，其他标签页包括主题项、标准扩展和自定义扩展。
2. **名称**：输入框中填写了“all”。
3. **证书类型**：选择框中选择了“双证书”。
4. **是否发布ldap**：有两个选项“是”和“否”，当前选择了“否”。
5. **是否发布ocsp**：同样有两个选项“是”和“否”，当前也选择了“否”。
6. **描述**：这是一个文本输入框，提示用户可以在此输入描述信息。

整个界面看起来像是一个证书管理或配置的系统页面，用户可以通过这个界面设置证书的相关参数。
```


#### 3. 删除证书模板

选中要删除的证书模板记录，点击删除按钮，等待程序响应，如成功，则弹出“删除证书模板成功！”提示并自动刷新列表，否则弹出对应的错误提示。

#### 4. 查找证书模板

<html><body><table><tr><td></td><td></td><td></td><td></td><td></td></tr><tr><td>名称 请输入</td><td>证书关型 请选择</td><td>√</td><td>查询</td><td>重置</td></tr></table></body></html>

根据名称，证书类型查询出相应的模版。

### 2.8 证书扩展管理

#### 2.8.1 证书扩展添加

业务管理员登录系统，添加自定义证书扩展，可在定义证书模板时加入自定义证书扩展，CA 在签发证书时，将自定义扩展写入证书的扩展属性里。

1. 选择菜单 证书模板 >自定义扩展管理，进入添加证书扩展页面，如下图所示。

![](images/63526572e6b5376a061f3e39489cf6d148c147586b3c7551532b1097c08cfda1.jpg)
```text
这是一张显示新增表单的图片，表单包含多个输入字段和选择框。具体来说，有以下几项：

1. 名称：这是一个必填字段，提示用户请输入内容。
2. 是否必填：这是一个选择框，默认选项为“否”。
3. 扩展oid：这也是一个必填字段，提示用户请输入内容。
4. 是否关键：这是一个选择框，默认选项为“否”。
5. 扩展值类型：这是一个必填的选择框，提示用户请选择内容。
6. 扩展值：这是一个非必填字段，提示用户请输入内容。

在表单的底部有两个按钮，分别是“确定”和“取消”，用户可以通过点击这两个按钮来提交或取消表单。
```


配置参数说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>名称</td><td>扩展的名称，取值要求有： 不能为空 长度要求1-32字符 只能包含汉字、字母、数字、下划线和空格，且不能以下划线和</td></tr><tr><td>是否必填</td><td>空格开头结尾</td></tr><tr><td>扩展OID</td><td>扩展是否是必填项，在证书里如果该扩展必填，则不能为空 扩展的 Object Identifier，用于在本 CA 体系中唯一标识该证 书扩展，不允许重复。一般的格式为×.X.X.X.，由2-10组点 和数字组成，以数字开始和结尾，第一组数字 1-9，其他组数字</td></tr><tr><td>是否关键</td><td>不能超过5位数，例如：1.2.52.2.11 扩展是否关键，在证书里如该扩展关键，需要断言是否满足要</td></tr></table></body></html>

数字证书认证系统用户手册

<html><body><table><tr><td></td><td>求，否则可不判断。</td></tr><tr><td>扩展值类型</td><td>选择扩展值类型</td></tr><tr><td>扩展值</td><td>扩展的取值。任一可打印字符，不能为空，最长256个字符</td></tr></table></body></html>

2. 根据实际填写扩展信息。3. 点击 提交 按钮，等待程序响应。

#### 2.8.2 证书扩展管理

证书扩展管理列举已添加的证书扩展列表。通过本列表可以对证书扩展管理，包括添加、修改、删除、查看和查找。

1. 选择菜单 证书模板管理 > 自定义扩展管理，进入证书扩展列表页面，如下图所示。

![](images/2b3cd339d539d35f2d0534b2265c110fbd5dcd3570d2eaca9aa671b9b319d829.jpg)
```text
这张图片显示了一个简单的用户界面元素，具体来说是一个文本输入框。在输入框的左侧有一个标签，上面写着“名称”，这表明这个输入框是用来输入名称的。输入框内有灰色的提示文字“请输入”，这是常见的占位符文本，用于提示用户在此处输入信息。整个界面看起来简洁明了，设计风格偏向于现代和简约。
```


新增  

<html><body><table><tr><td>名称</td><td>是香必填</td><td>扩展oid</td><td>是否关键</td><td>扩展值类型</td><td>扩展值</td><td>创建时问</td><td>操作</td></tr><tr><td>all</td><td>香</td><td>*******</td><td>香</td><td>STRING</td><td>12341234</td><td>2023-09-21 16:46:32</td><td>编辑删除</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

列表说明如下表所示。

<html><body><table><tr><td>配置项</td><td>说明</td></tr><tr><td>名称</td><td>扩展的名称，可重复</td></tr><tr><td>是否必填</td><td>扩展值是否必须填写，默认必须填写</td></tr><tr><td>扩展OID</td><td>扩展的 Object Identifier，对象唯一标识</td></tr><tr><td>是否关键</td><td>扩展是否关键</td></tr><tr><td>扩展值类型</td><td>扩展值类型</td></tr><tr><td>扩展值</td><td>扩展的值</td></tr><tr><td>创建时间</td><td>创建扩展的时间</td></tr><tr><td>修改时间</td><td>最后一次修改扩展的时间</td></tr></table></body></html>

2. 修改证书扩展。

选择要修改的证书扩展项，点击编辑按钮，跳转到证书扩展编辑页面。根据实际情况修改证书扩展信息，输入项与添加证书扩展要求一样。如下图所示：、

![](images/d5f7ccfdb775a09598a079c1469b4b66c424e16010ca8da0c4078722cd5f10c2.jpg)
```text
这张图片显示了一个表单界面，包含多个输入字段和选项。具体如下：

1. **名称**：输入框中填写了“all”。
2. **是否必填**：选择框中选择了“否”。
3. **扩展oid**：输入框中填写了“*******”。
4. **是否关键**：选择框中选择了“否”。
5. **扩展值类型**：选择框中选择了“STRING”。
6. **扩展值**：输入框中填写了“12341234”。

在表单的底部有两个按钮：“确定”和“取消”。
```
  
编辑

#### 3. 删除证书扩展

<html><body><table><tr><td>名称</td><td>是否必填</td><td>扩展oid</td><td>是否关键</td><td>扩展值类型</td><td>扩展值</td><td>创建时问</td><td>操作</td></tr><tr><td>all</td><td>否</td><td>*******</td><td>否</td><td>STRING</td><td>12341234</td><td>2023-09-21 16:46:32</td><td>编辑删除</td></tr></table></body></html>

选中要删除的证书扩展记录，点击删除按钮，等待程序响应。

4. 查找证书扩展

名称

查询

输入名称，点击查询按钮，按条件查找到预期的项。

#### 2.8.3 证书模板列表

![](images/3c8c844fbbc8c943a6dd44a32518cdc1966da9acdc0852cb0c24bc5a7077ebde.jpg)
```text
这是一张显示密码服务管理平台界面的截图。界面左侧是一个垂直导航栏，列出了多个管理选项，包括数据库加密、时间戳、协同签名、动态令牌、电子签章、SSLVPN加密通道、数字证书认证、CRL配置、CRL列表、证书申请、证书管理、证书下载、机构管理、证书模板、自定义扩展管理、资源信息、SIM盾管理、SIMKEY管理、用户管理、系统管理和监控管理。

右侧是证书模板管理页面，顶部有一个搜索框和一些标签页，如首页、有效证书下载、注销证书下载、过期证书下载、CA证书管理、RA管理、证书更新申请、证书注销申请、资源信息和证书模板管理。在搜索框下方，有一个表格，列出了证书模板的名称、证书类型、描述、创建时间和操作选项。表格中有几行数据，每行代表一个证书模板，显示了其名称、类型、描述和创建时间，并提供了编辑和删除的操作链接。

页面右上角显示了许可证有效期为2030年11月1日，以及当前操作员的用户名为oper。
```


## 2.9 资源信息

在数字证书认证菜单下选择资源信息菜单，显示数字证书认证使用的资源信息，包含版本信息，做数字证书认证业务连接的业务地址，数字证书认证 API 的下载按钮，数字证书认证使用的服务和设备信息。

![](images/9cbbdc0f2b117b74c99d2c6f5b06b82c67a242a54d8d7fc52fd4010a412ae907.jpg)
```text
这是一张显示密码服务管理平台界面的截图。界面上方显示了当前用户的操作员名称为“oper”，并且许可证有效期至2030年11月1日。左侧有一个导航栏，列出了多个功能模块，包括动态令牌、电子签章、SSLVPN加密通道、数字证书认证、CRL配置、证书申请、证书管理、资源信息、SIM盾管理、SIMKEY管理、用户管理和系统管理等。

在主内容区域，顶部有几个标签页，分别是首页、有效证书下载、注销证书下载、过期证书下载、CA证书管理、RA管理、证书更新申请和证书注销申请。当前选中的标签页是“资源信息”。

在“资源信息”标签页下，有三个主要部分：版本信息、服务信息和设备信息。

1. **版本信息**：
   - 产品名称：数字证书认证服务
   - 产品型号：SZT1701
   - 软件版本：V5.0.1.1

2. **服务连接地址**：
   - 基础不选组显示在全部组下（restful）: https://***********:8866

3. **服务信息**：
   - 列表中有一条记录，序号为1，服务名称为CA，服务类型为数字证书认证业务，所属服务组为ele，服务IP为***********，业务端口为20801，创建时间为2023-11-01 14:31:27。

4. **设备信息**：
   - 列表中有三条记录，分别对应不同的设备名称、设备类型、所属设备组、管理IP、管理端口和所属厂商，以及创建时间。

整体来看，这张图片展示了一个用于管理和监控数字证书认证服务的平台界面，包含了版本信息、服务连接地址、服务信息和设备信息等内容。
```


# 公司介绍

三未信安科技股份有限公司（股票代码：688489）成立于2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。

三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码芯片、PCI-E 接口密码卡、服务器密码机、金融数据密码机、签名验签服务器、云密码机、数据库加密机、SSL VPN、IPSec VPN、密 钥 管 理 系 统 、 密 码 服 务 平 台 、 身 份 认 证 系 统 等 ， 全 面 支 持SM1 、 SM2 、 SM3 、 SM4 、 SM7 、 SM9 、 ZUC 等 国 产 密 码 算 法 和RSA、ECC、AES、SHA 等国际密码算法，为关键信息基础设施和重要信息系统提供安全的密码运算和完善的密钥管理机制。三未信安在云计算、大数据、物联网、车联网、人工智能、区块链、隐私保护计算、数字货币等新兴技术领域进行了积极的技术创新，并推出了一系列新型密码解决方案。

经过十几年的市场开拓，三未信安的产品和服务赢得了客户和市场的认可，产品已广泛应用于金融、证券、能源、电信、交通、电子商务等行业，以及海关、公安、税务、水利、医疗保障等政府部门。

三未信安是国家级高新技术企业、国家级专精特新重点“小巨人”企业，公司研发了国内首款安全三级密码板卡和首款安全三级密码机，公司的密码机通过了 FIPS 140-2Level3（美国联邦信息处理标准3 级）认证，荣获五次国家密码科技进步奖。公司是全国信息安全标准化技术委员会和密码行业标准化技术委员会成员单位，牵头和参与制定了二十余项密码领域国家标准或行业标准。

三未信安坚持“做客户信赖的公司，做有核心技术的公司，做员工热爱的公司”的发展理念，恪守“让生活更美好，做对社会真正有价值的事情”的价值追求，以“用密码技术守护数字世界”为使命，凝聚人才、锐意进取，立志为我国的网络信息安全事业贡献自己的力量！