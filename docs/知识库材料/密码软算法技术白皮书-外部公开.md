密码软算法产品技术白皮书

# 版权声明

版权所有 $\circledcirc$ 三未信安科技股份有限公司 2024 保留一切权利（包括但不限于修订、最终解释权）。

本文档由三未信安科技股份有限公司编写，仅用于用户和合作伙伴阅读。本公司依中华人民共和国著作权法，享有及保留一切著作之专属权利。未经本公司书面许可，任何单位和个人不得以任何方式或形式对文档内任何部分或全部内容进行擅自摘抄、增删、复制、仿制、备份和修改，并不得以任何形式传播。

# 特别提示

由于产品版本升级或其他原因，本文档内容会不定期更新，更新的内容会在本文档发行新版本时予以印刷。本文档仅用于为最终用户提供信息或使用指导，文档中的陈述、信息和建议不构成任何明示或暗示的担保。您所购买产品的硬件配置、功能、特性或服务等应受本公司商业合同和条款约束。本文档中描述的硬件配置、功能、特性或服务可能不在您的购买或使用范围之内。任何情况下，本公司均不对（包括但不限于）最终用户或任何第三方因使用本文档而造成的直接和间接损失或损害负责。

# 联系我们

感谢您使用我们的产品，如果您对我们的产品有什么意见和建议，可以通过电话、传真或电子邮件等方式向我们反馈。

电话：400-00-90196  
邮箱：<EMAIL>  
网址：www.sansec.com.cn  
公司地址、各地分公司与办事处地址请前往官方网站查阅。

# 目录

1. 产品简介..

1.1 技术背景. 1  
1.2 适用场景..  
1.2.1 高性能软算法.  
1.2.2 白盒算法.  
2. 产品特性. 1  
3. 产品功能.. 3  
3.1 密码算法. 3  
4. 技术指标.. 3  
4.1 性能参数. 4  
5. 部署方式. 10  
6. 典型应用.. 10  
6.1 应用1：中国某银行. 10  
6.2 应用2：某银行.. .10  
7. 产品资质. 10  
8. 技术标准. 11  
公司介绍.. 12

### 外部公开

# 1. 产品简介

### 1.1 技术背景

目前，国家商用密码算法的应用大部分需要依赖专用硬件密码模块，如 USB Key、密码机等。但也存在一些应用场景由于客观因素的限制，需要在无法使用专用密码模块的情况下保证应用场景各环节的安全；或者，由于所需要解决的问题较小，以及成本等因素考虑，不愿意引入密码硬件系统解决。同时，对于开源的密码算法库，由于其体量庞大且欠缺技术支持和保障而不便使用。

因此，一方面是对灵活、轻量的标准高性能密码软算法，另一方面是在设备条件不支持硬件，但仍需提供密码安全保护的白盒加固密码算法，在这两方面的需求下，产生了三未信安密码软算法模块。

### 1.2 适用场景

## 1.2.1 高性能软算法

高性能密码软算法模块支持 RSA/SHA/AES/3DES 等国际算法以及 SM2/SM3/SM4 等国密算法，可运行在相对安全环境下，密钥的安全由使用者自行保证的场景。

## 1.2.2 白盒算法

白盒密码算法模块支持RSA/AES 等国际算法以及SM2/SM4 等国密算法。可运行在终端设备，或需要自行存储非明文密钥文件的场景。白盒密码算法，将原始密钥导出为混淆加固的白盒密钥，从而可落成文件存储到设备本地，该混淆密钥文件只能通过白盒算法库调用，才能实现其作为密钥的原始功能，如，对于对称算法而言是加解密，对于非对称算法而言是签名或解密等私钥运算（公钥运算无混淆保护）。

同时，白盒算法支持传入设备信息以实现设备信息与白盒密钥的绑定。当白盒密钥绑定了某台设备上获取的设备信息时，即便将白盒密钥与白盒算法库同时拷贝到另一台设备上使用，若另一台设备上获取输入的信息不同，也将无法实现密钥正常的功能，如，对称算法加解密得到错误的结果，非对称算法签名或解密等运算得到错误结果。

# 2. 产品特性

$\bullet$ 通用接口

上层使用通用框架层，屏蔽底层算法接口细节，保证客户侧丰富的应用程序直接调用框架层便可实现密码运算功能，无需关注底层细节；

$\bullet$ 集成升级便捷

产品以动态库或静态库的形式提供集成对接，集成简单。并且使用动态库的情况下，升级算法库只需替换动态库，无需改动框架层接口，升级灵活便捷。

灵活裁剪

密码算法软件模块，可依据客户实际需要的算法模块进行裁剪交付，如客户只需要国密的白盒算法模块，则提供的算法库将不包含国际算法与高性能标准算法。

支持白盒运算

安全性

白盒算法对各算法密钥，结合算法本身采取不同的方式进行了混淆加固。且从算法层面，对白盒密钥的生成过程与使用过程做了加固。从而使得无法从白盒密钥文件中观察出原始密钥，并且难以通过逆向分析恢复明文密钥。

设备绑定

白盒算法支持设备绑定，即白盒密钥文件的防拷贝。假如有两台不同的设备均安装了本产品中的白盒算法，但若采用了设备绑定机制，则其中一台设备生成的白盒密钥文件，拷贝到另一台设备上将无法正常使用。

密钥的更新替换  
支持白盒密钥的更新替换，无需替换库文件，仅通过替换存储的白盒文件，即可实现密钥更新。

多平台支持

可有效适用于Windows，Linux，ARM，龙芯平台系统，可以有效应用于传统应用，移动互联网，物联网等领域。

# 3. 产品功能

### 1.1 密码算法

<html><body><table><tr><td>算法 类别</td><td>算法名称</td><td>China /US</td><td>密钥强度</td><td>算法模式</td></tr><tr><td rowspan="2">哈希 摘要</td><td>SHA1/256/384/5 12</td><td>US</td><td>-</td><td>摘要</td></tr><tr><td>SM3</td><td>China</td><td></td><td>摘要</td></tr><tr><td rowspan="2">HMA C</td><td>SHA1/256/384/5 12-HMAC</td><td>US</td><td>大于0</td><td>HMAC</td></tr><tr><td>SM3-HMAC</td><td>China</td><td>大于0</td><td>HMAC</td></tr><tr><td rowspan="2">非对 称</td><td>RSA1024/2048/4 096</td><td>US</td><td>1024/2048/40 96</td><td>签名/验签/加密/解密/密钥对生成</td></tr><tr><td>SM2</td><td>China</td><td>256</td><td>签名/验签/加密/解密/密钥对生成</td></tr><tr><td rowspan="2">对称</td><td>AES</td><td>US</td><td>128/192/256</td><td>ECB/CBC/CTR/GCM/CCM/OFB/</td></tr><tr><td>3DES</td><td>US</td><td>128/192</td><td>CFB/XTS/BC/OCB加解密 ECB/CBC/CTR/OFB/CFB 加解密</td></tr></table></body></html>

密码软算法技术白皮书  

<html><body><table><tr><td></td><td>SM4</td><td>China</td><td>128</td><td>ECB/CBC/CTR/GCM/CCM/OFB/ CFB/XTS/BC/OCB加解密</td></tr><tr><td rowspan="2">非对 称</td><td>wb-RSA</td><td>-</td><td>1024/2048</td><td>签名/解密</td></tr><tr><td>wb-SM2</td><td>_</td><td>256</td><td>签名/解密</td></tr><tr><td rowspan="2">白盒 对称 白盒</td><td>wb-AES</td><td>-</td><td>128/256</td><td>ECB/CBC/CTR/OFB加解密</td></tr><tr><td>wb-SM4</td><td></td><td>128</td><td>ECB/CBC/CTR/OFB加解密</td></tr></table></body></html>

# 4. 技术指标

※ 本章节数据均为动态库测试下的数据，且测试环境如下：

<html><body><table><tr><td>测试环境</td><td>配置</td></tr><tr><td>操作系统</td><td>CentOS 7.6</td></tr><tr><td>内存</td><td>128GB RAM</td></tr><tr><td>CPU</td><td>Intel(R) Xeon(R) Gold 5218 CPU @ 2.30GHz</td></tr></table></body></html>

### 4.1 性能参数

<html><body><table><tr><td colspan="2">非对称算法性能指标（32线程）</td></tr><tr><td>SM2 密钥对生成 (Tps)</td><td>12000000</td></tr><tr><td>SM2(签名/验证)(Tps)</td><td>610000/280000</td></tr><tr><td>SM2 加密/解密(Mbps)@1024Bytes</td><td>600/8850</td></tr><tr><td>RSA4096密钥对生成 (Tps)</td><td>10</td></tr><tr><td>RSA2048密钥对生成 (Tps)</td><td>120</td></tr><tr><td>RSA1024密钥对生成（Tps)</td><td>800</td></tr><tr><td>RSA4096(签名/验证)(Tps)</td><td>1500/90000</td></tr><tr><td>RSA2048(签名/验证)(Tps)</td><td>11000/300000</td></tr><tr><td>RSA1024(签名/验证) (Tps)</td><td>70000/700000</td></tr><tr><td>RSA4096(加密/解密)(Mbps) @4096Bits</td><td>300/7</td></tr><tr><td>RSA2048(加密/解密)(Mbps) @2048Bits</td><td>600/20</td></tr><tr><td>RSA1024(加密/解密)(Mbps)@1024Bits</td><td>700/70</td></tr><tr><td colspan="2">哈希算法性能指标 (32线程） (Mbps)@8192Bytes</td></tr></table></body></html>

密码软算法技术白皮书  

<html><body><table><tr><td>SM3</td><td>28000</td></tr><tr><td>SHA1</td><td>55000</td></tr><tr><td>SHA25</td><td>19000</td></tr><tr><td>SHA38</td><td>36000</td></tr><tr><td>SHA51</td><td>35000</td></tr><tr><td colspan="2">对称算法性能指标（32线程）(Mbps)@8192Bytes</td></tr><tr><td>SM4 ECB/CBC</td><td>17000</td></tr><tr><td>AES ECB/CBC</td><td>115000</td></tr><tr><td>3DES ECB/CBC</td><td>4200</td></tr></table></body></html>

# 5. 部署方式

产品以动态库或静态库的形式提供集成对接。动态库的情况下，只需将库文件拷贝至系统库路径下，并在程序编译时标明引用；静态库则由开发人员直接引用编译即可。

# 6. 典型应用

### 6.1 应用1：中国某银行

三未信安从某银行APP 生物识别加密项目快速接入，与该银行联合研发软件加密模块。该项目有效在行内包括各类 Android/iOS 终端及应用服务中完成密码模块行内推广应用。

### 6.2 应用2：某银行

三未信安软件密码模块在某银行应用国产密码模块，应用包括 JS、Java、C、iOS、Android、Windows 各方面，该银行将原自研算法模块安全、有效地切换到三未国密认证算法模块。

# 7. 产品资质

密码软算法产品具备以下资质：

商用密码产品认证证书（软件密码模块 SJM1920 V4）

# 8. 技术标准

密码软算法产品遵循并符合如下技术规范与要求：

GM/T 0002-2012 SM4 分组密码算法

密码软算法技术白皮书

GM/T 0003-2012 SM2 椭圆曲线公钥密码算法  
GM/T 0004-2012 SM3 密码杂凑算法  
GM/T 0006-2012 密码应用标识规范  
GM/T 0009-2012 SM2 密码算法使用规范  
GM/T 0010-2012 SM2 密码算法加密签名消息语法规范  
GM/T 0028-2014 密码模块安全技术要求  
GM/T 0039-2015 密码模块安全检测要求

# 公司介绍

三未信安科技股份有限公司（股票代码：688489）成立于2008 年，是国内主要的密码基础设施提供商，专注于密码技术的研究创新和核心产品的开发、销售及服务，为用户提供全面的商用密码产品和整体解决方案。

三未信安具备从密码芯片、密码板卡、密码整机到密码系统的完整密码产品体系和信创密码建设能力，当前已有五十多款产品取得了国家商用密码产品认证证书，是商用密码产品种类最齐全的公司之一。典型产品包括密码芯片、PCI-E 接口密码卡、服务器密码机、金融数据密码机、签名验签服务器、云密码机、数据库加密机、SSL VPN、IPSec VPN、密钥管理系统、密码服务平台、身份认证系统等，全面支持 SM1、SM2、SM3、SM4、SM7、SM9、ZUC 等国产密码算法和RSA、ECC、AES、SHA 等国际密码算法，为关键信息基础设施和重要信息系统提供安全的密码运算和完善的密钥管理机制。三未信安在云计算、大数据、物联网、车联网、人工智能、区块链、隐私保护计算、数字货币等新兴技术领域进行了积极的技术创新，并推出了一系列新型密码解决方案。

经过十几年的市场开拓，三未信安的产品和服务赢得了客户和市场的认可，产品已广泛应用于金融、证券、能源、电信、交通、电子商务等行业，以及海关、公安、税务、水利、医疗保障等政府部门。

三未信安是国家级高新技术企业、国家级专精特新重点“小巨人”企业，公司研发了国内首款安全三级密码板卡和首款安全三级密码机，公司的密码机通过了 FIPS 140-2Level3（美国联邦信息处理标准3 级）认证，荣获五次国家密码科技进步奖。公司是全国信息安全标准化技术委员会和密码行业标准化技术委员会成员单位，牵头和参与制定了二十余项密码领域国家标准或行业标准。

三未信安坚持“做客户信赖的公司，做有核心技术的公司，做员工热爱的公司”的发展理念，恪守“让生活更美好，做对社会真正有价值的事情”的价值追求，以“用密码技术守护数字世界”为使命，凝聚人才、锐意进取，立志为我国的网络信息安全事业贡献自己的力量！