<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件预览 - {{ filename }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .preview-container {
            max-height: 80vh;
            overflow-y: auto;
        }
        .table {
            font-size: 0.875rem;
        }
        .table th {
            background-color: #045ab6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .markdown-content {
            line-height: 1.6;
        }
        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        .markdown-content table {
            width: 100%;
            margin-bottom: 1rem;
        }
        .markdown-content table th,
        .markdown-content table td {
            padding: 0.5rem;
            border: 1px solid #dee2e6;
        }
        .markdown-content table th {
            background-color: #f8f9fa;
        }
        .markdown-content pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        .markdown-content code {
            background-color: #f8f9fa;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875em;
        }
        .markdown-content blockquote {
            border-left: 4px solid #007bff;
            padding-left: 1rem;
            margin-left: 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="bi bi-eye me-2"></i>文件预览</h2>
                        <p class="text-muted mb-0">{{ filename }}</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-secondary" onclick="window.close()">
                            <i class="bi bi-x-lg me-1"></i>关闭
                        </button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-body p-0">
                        <div class="preview-container p-3">
                            {% if filename.endswith('.md') %}
                            <div class="markdown-content">
                                {{ content|safe }}
                            </div>
                            {% else %}
                            <div class="table-responsive">
                                {{ content|safe }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                {% if filename.endswith(('.xlsx', '.xls')) %}
                <div class="alert alert-info mt-3">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>提示：</strong>仅显示前100行数据，完整数据请下载文件查看。
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
