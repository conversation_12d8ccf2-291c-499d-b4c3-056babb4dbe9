# COSMIC功能需求文档生成器

基于COSMIC功能点分析方法的自动化功能需求文档生成工具。该工具可以将COSMIC功能拆解数据自动转换为结构化的功能需求文档，包括功能描述、时序图等。

## 项目简介

本项目旨在解决软件开发过程中功能需求文档编写效率低下的问题。通过分析COSMIC功能拆解数据，自动生成标准化的功能需求文档，大大减少需求分析师的工作量，提高文档编写效率和质量一致性。

## 功能特性

- **自动化文档生成**：基于COSMIC功能拆解数据自动生成功能需求文档
- **结构化输出**：按照标准目录结构生成Markdown格式文档
- **时序图生成**：自动为三级模块生成业务时序图（Mermaid语法）
- **批量处理**：支持大批量数据分批次处理，避免大模型处理超限
- **多线程支持**：支持并发处理，提高生成效率
- **知识库集成**：可集成领域知识库，提升生成质量
- **灵活配置**：支持自定义起始编号、批处理大小等参数

## 安装依赖

```
pip install -r requirements.txt
```

## 使用方法

```
python run_cosmic_generator.py
```

## 配置说明

```
# 通用配置
INPUT_FILE = "output-new.csv"  # 输入的COSMIC功能拆解CSV文件
OUTPUT_DIR = "output"          # 输出目录

# 时序图生成配置
SEQUENCE_DIAGRAM_ENABLED = True  # 是否生成时序图
SEQUENCE_DIAGRAM_FORMAT = "```

# 批处理配置
BATCH_SIZE = 100  # 每批处理的数据量
MAX_THREADS = 4   # 最大并发线程数

# 其他配置
START_NUMBER = 1  # 文档编号起始值

```

## 输出说明

```
功能描述:
  一级模块/二级模块/三级模块
  功能过程名称

时序图:
```
